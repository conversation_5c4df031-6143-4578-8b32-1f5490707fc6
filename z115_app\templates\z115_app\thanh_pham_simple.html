{% load static %}
{% load custom_filters %}
{% load humanize %}
<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>THÀNH PHẨM PVSX | Z115 System</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Bootstrap 5.3.7 -->
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{% static 'css/all.min.css' %}">
    <!-- AdminLTE -->
    <link rel="stylesheet" href="{% static 'css/adminlte.min.css' %}">

    <!-- Custom CSS -->
    <style>
        /* Tăng cỡ chữ cho input date */
        input[type="date"] {
            font-size: 18px !important;
            font-weight: 500 !important;
            padding: 8px 12px !important;
        }

        /* Đảm bảo text trong input date hiển thị đúng cỡ chữ */
        input[type="date"]::-webkit-datetime-edit {
            font-size: 18px !important;
        }

        input[type="date"]::-webkit-datetime-edit-text {
            font-size: 18px !important;
        }

        input[type="date"]::-webkit-datetime-edit-month-field {
            font-size: 18px !important;
        }

        input[type="date"]::-webkit-datetime-edit-day-field {
            font-size: 18px !important;
        }

        input[type="date"]::-webkit-datetime-edit-year-field {
            font-size: 18px !important;
        }
    </style>

    <style>
        /* Global font size 16px */
        body,
        .content-wrapper,
        .small-box,
        .card,
        .table,
        .modal-body,
        .form-control,
        .btn,
        label,
        p,
        h5,
        h6 {
            font-size: 16px !important;
        }

        /* Statistics cards */
        .small-box .inner h3 {
            font-size: 20px !important;
            font-weight: bold;
        }

        .small-box .inner p {
            font-size: 18px !important;
        }

        .small-box-footer {
            font-size: 18px !important;
        }

        /* Tables */
        .table th,
        .table td {
            font-size: 16px !important;
        }

        /* Modal headers */
        .modal-title {
            font-size: 18px !important;
        }

        .content-wrapper {
            margin-left: 250px;
        }

        .main-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            z-index: 1000;
        }

        .navbar {
            margin-left: 250px;
        }

        .small-box {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .small-box-footer {
            position: relative;
            text-align: center;
            padding: 3px 0;
            color: #fff;
            color: rgba(255, 255, 255, 0.8);
            display: block;
            z-index: 10;
            background: rgba(0, 0, 0, 0.1);
            text-decoration: none;
        }

        .small-box-footer:hover {
            color: #fff;
            background: rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }

        .bg-purple {
            background-color: #6f42c1 !important;
        }

        /* Form filter styling */
        .gap-3 {
            gap: 1rem !important;
        }

        .form-group {
            margin-bottom: 0 !important;
        }

        .form-group .form-control {
            font-family: 'Times New Roman', serif !important;
            font-size: 14px !important;
        }

        /* Font Times New Roman cho toàn bộ dashboard */
        .content-wrapper,
        .card,
        .table,
        .modal,
        .alert,
        .small-box {
            font-family: 'Times New Roman', serif !important;
            font-size: 14px !important;
        }

        /* Sidebar styling */
        .nav-sidebar .nav-link {
            font-family: 'Times New Roman', serif !important;
            font-size: 18px !important;
            color: white !important;
            font-weight: bold !important;
            text-transform: uppercase !important;
        }

        .nav-sidebar .nav-link.active {
            background-color: #007bff !important;
            color: white !important;
            font-weight: bold !important;
        }

        .sidebar-dark-primary .nav-sidebar>.nav-item>.nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        .sidebar-dark-primary .nav-sidebar>.nav-item>.nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: white !important;
        }

        /* Brand link styling */
        .brand-link {
            font-family: 'Times New Roman', serif !important;
            font-size: 20px !important;
            font-weight: bold !important;
            text-transform: uppercase !important;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">

        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="{% url 'home' %}" class="nav-link">Trang chủ</a>
                </li>
            </ul>

            <!-- Right navbar links -->
            <ul class="navbar-nav ml-auto">
                <li class="nav-item">
                    <span class="navbar-text">Xin chào, {{ user.username }}</span>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'logout' %}">
                        <i class="fas fa-sign-out-alt"></i> Đăng xuất
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- Brand Logo -->
            <a href="{% url 'thanh_pham' %}" class="brand-link">
                <i class="fas fa-industry brand-image"></i>
                <span class="brand-text font-weight-light">THÀNH PHẨM PVSX</span>
            </a>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu"
                        data-accordion="false">
                        <li class="nav-item">
                            <a href="{% url 'thanh_pham' %}" class="nav-link active">
                                <i class="nav-icon fas fa-industry"></i>
                                <p>Dashboard Thành phẩm</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'san_xuat_thanh_pham' %}" class="nav-link">
                                <i class="nav-icon fas fa-cogs"></i>
                                <p>Sản xuất thành phẩm</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'tieu_thu' %}" class="nav-link">
                                <i class="nav-icon fas fa-truck-loading"></i>
                                <p>Tiêu thụ thành phẩm</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'danh_muc_thanh_pham' %}" class="nav-link">
                                <i class="nav-icon fas fa-list"></i>
                                <p>Danh mục thành phẩm</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'ton_kho_thanh_pham' %}" class="nav-link">
                                <i class="nav-icon fas fa-warehouse"></i>
                                <p>Tồn kho thành phẩm</p>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Content Header -->
            <div class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-12 text-center">
                            <h1 class="m-0"
                                style="font-family: 'Times New Roman', serif; font-size: 22px; font-weight: bold; text-transform: uppercase; color: #007bff;">
                                NXT KHO THÀNH PHẨM XÍ NGHIỆP I
                            </h1>
                        </div>
                    </div>
                    <div class="row mb-4 justify-content-center">
                        <div class="col-12 text-center">
                            <form method="GET" class="form-inline justify-content-center" style="font-size: 16px;">
                                <div class="form-group mr-3">
                                    <label for="from_date" class="mr-2 font-weight-bold" style="color: #007bff;">TỪ
                                        NGÀY:</label>
                                    <input type="date" class="form-control" id="from_date" name="from_date"
                                        value="{{ from_date_str }}" style="font-size: 18px;">
                                </div>
                                <div class="form-group mr-3">
                                    <label for="to_date" class="mr-2 font-weight-bold" style="color: #007bff;">ĐẾN
                                        NGÀY:</label>
                                    <input type="date" class="form-control" id="to_date" name="to_date"
                                        value="{{ to_date_str }}" style="font-size: 18px;">
                                </div>
                                <button type="submit" class="btn btn-primary" style="font-size: 16px;">
                                    <i class="fas fa-search"></i> LỌC
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">

                    <!-- Statistics Cards -->
                    <div class="row justify-content-center mx-2">
                        <div class="col-lg col-md-4 col-sm-6 col-12 mb-3 px-2">
                            <div class="small-box bg-info">
                                <div class="inner text-center">
                                    <h3>{{ total_san_xuat|dot_format }}</h3>
                                    <p>Tổng sản xuất trong kỳ</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <a href="#" class="small-box-footer text-center" data-toggle="modal"
                                    data-target="#modalTongSanXuat">
                                    <i class="fas fa-eye"></i> XEM
                                </a>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6 col-12 mb-3 px-2">
                            <div class="small-box bg-success">
                                <div class="inner text-center">
                                    <h3>{{ total_tieu_thu|dot_format }}</h3>
                                    <p>Tổng tiêu thụ trong kỳ</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-truck-loading"></i>
                                </div>
                                <a href="#" class="small-box-footer text-center" data-toggle="modal"
                                    data-target="#modalTongTieuThu">
                                    <i class="fas fa-eye"></i> XEM
                                </a>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6 col-12 mb-3 px-2">
                            <div class="small-box bg-purple">
                                <div class="inner text-center">
                                    <h3>{{ total_ton_kho }}</h3>
                                    <p>Tồn kho trong kỳ</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-warehouse"></i>
                                </div>
                                <a href="#" class="small-box-footer text-center" data-toggle="modal"
                                    data-target="#modalTonKho">
                                    <i class="fas fa-eye"></i> XEM
                                </a>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6 col-12 mb-3 px-2">
                            <div class="small-box bg-warning">
                                <div class="inner text-center">
                                    <h3>{{ total_khach_hang }}</h3>
                                    <p>Khách hàng</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <a href="#" class="small-box-footer text-center" data-toggle="modal"
                                    data-target="#modalKhachHang">
                                    <i class="fas fa-eye"></i> XEM
                                </a>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6 col-12 mb-3 px-2">
                            <div class="small-box bg-danger">
                                <div class="inner text-center">
                                    <h3>{{ canh_bao_detail|length }}</h3>
                                    <p>Cảnh báo hết hàng</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <a href="#" class="small-box-footer text-center" data-toggle="modal"
                                    data-target="#modalCanhBao">
                                    <i class="fas fa-eye"></i> XEM
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Charts Section -->
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <!-- Biểu đồ Tiêu thụ thành phẩm -->
                        <div class="col-lg-6 col-md-12">
                            <div class="card">
                                <div class="card-header bg-success">
                                    <h3 class="card-title">
                                        <i class="fas fa-chart-bar"></i>
                                        Biểu đồ Tiêu thụ thành phẩm từ ngày {{ from_date|date:"d/m/Y" }} đến
                                        ngày {{ to_date|date:"d/m/Y" }}
                                    </h3>
                                </div>
                                <div class="card-body">
                                    <canvas id="chartTieuThu" style="height: 400px;"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Biểu đồ Hộ khách hàng tiêu thụ -->
                        <div class="col-lg-6 col-md-12">
                            <div class="card">
                                <div class="card-header bg-warning">
                                    <h3 class="card-title">
                                        <i class="fas fa-chart-pie"></i>
                                        Biểu đồ Hộ khách hàng tiêu thụ từ ngày {{ from_date|date:"d/m/Y" }} đến
                                        ngày {{ to_date|date:"d/m/Y" }}
                                    </h3>
                                </div>
                                <div class="card-body">
                                    <canvas id="chartKhachHang" style="height: 400px;"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Footer -->
        <footer class="main-footer">
            <strong>@ 2025 Copyright by Trần Đình Hưng - Phòng Vật tư - Nhà máy Z115 - Tổng cục CNQP</strong>
            <div class="float-right d-none d-sm-inline-block">
                <b>Version</b> 1.0.0
            </div>
        </footer>
    </div>

    <!-- Modals -->
    <!-- Modal Tổng sản xuất -->
    <div class="modal fade" id="modalTongSanXuat" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h4 class="modal-title">Chi tiết Tổng sản xuất trong kỳ từ ngày {{ from_date|date:"d/m/Y" }} đến
                        ngày {{ to_date|date:"d/m/Y" }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                        <table class="table table-bordered table-striped align-middle">
                            <thead class="table-info sticky-top">
                                <tr>
                                    <th>STT</th>
                                    <th>Ngày tháng</th>
                                    <th>Tên thành phẩm</th>
                                    <th>Đvt</th>
                                    <th>Số lượng sản xuất</th>
                                    <th>Phân xưởng sản xuất</th>
                                    <th>Ghi chú</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in san_xuat_detail_list %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ item.ngay_san_xuat|date:"d/m/Y" }}</td>
                                    <td>{{ item.thanh_pham.ten_thanh_pham }}</td>
                                    <td>{{ item.thanh_pham.don_vi_tinh }}</td>
                                    <td class="text-end">{{ item.so_luong|dot_format }}</td>
                                    <td>{{ item.phan_xuong_san_xuat }}</td>
                                    <td>{{ item.ghi_chu }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">Không có dữ liệu sản xuất trong khoảng thời gian
                                        này.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-info">
                                <tr>
                                    <th colspan="4" class="text-end"><strong>TỔNG CỘNG:</strong></th>
                                    <th class="text-end"><strong>{{ total_san_xuat|dot_format }}</strong></th>
                                    <th colspan="2"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Tổng tiêu thụ -->
    <div class="modal fade" id="modalTongTieuThu" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h4 class="modal-title">Chi tiết Tổng tiêu thụ trong kỳ từ ngày {{ from_date|date:"d/m/Y" }} đến
                        ngày {{ to_date|date:"d/m/Y" }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                        <table class="table table-bordered table-striped align-middle">
                            <thead class="table-success sticky-top">
                                <tr>
                                    <th>STT</th>
                                    <th>Ngày tháng</th>
                                    <th>Tên thành phẩm</th>
                                    <th>Đvt</th>
                                    <th>Số lượng tiêu thụ</th>
                                    <th>Hộ khách hàng</th>
                                    <th>Nơi tiêu thụ</th>
                                    <th>Ghi chú</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in tieu_thu_detail_list %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ item.ngay_tieu_thu|date:"d/m/Y" }}</td>
                                    <td>{{ item.thanh_pham.ten_thanh_pham }}</td>
                                    <td>{{ item.thanh_pham.don_vi_tinh }}</td>
                                    <td class="text-end">{{ item.so_luong|dot_format }}</td>
                                    <td>{{ item.ho_khach_hang }}</td>
                                    <td>{{ item.noi_tieu_thu }}</td>
                                    <td>{{ item.ghi_chu }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">Không có dữ liệu tiêu thụ trong khoảng thời gian
                                        này.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-success">
                                <tr>
                                    <th colspan="4" class="text-end"><strong>TỔNG CỘNG:</strong></th>
                                    <th class="text-end"><strong>{{ total_tieu_thu|dot_format }}</strong></th>
                                    <th colspan="3"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Tồn kho -->
    <div class="modal fade" id="modalTonKho" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-purple text-white">
                    <h4 class="modal-title">Chi tiết Tồn kho trong kỳ từ ngày {{ from_date|date:"d/m/Y" }} đến
                        ngày {{ to_date|date:"d/m/Y" }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                        <table class="table table-bordered table-striped align-middle">
                            <thead class="bg-purple text-white sticky-top">
                                <tr>
                                    <th>STT</th>
                                    <th>Tên thành phẩm</th>
                                    <th>Đvt</th>
                                    <th class="text-center">Tồn đầu kỳ ngày {{ from_date|date:"d/m/Y" }}</th>
                                    <th class="text-center">Tổng sản xuất</th>
                                    <th class="text-center">Tổng tiêu thụ</th>
                                    <th class="text-center">Tồn cuối XN1</th>
                                    <th>Ghi chú</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in ton_kho_detail_list %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ item.ten_thanh_pham }}</td>
                                    <td>{{ item.don_vi_tinh }}</td>
                                    <td class="text-center">{{ item.ton_dau|dot_format_hide_zero }}</td>
                                    <td class="text-center">{{ item.tong_san_xuat|dot_format_hide_zero }}</td>
                                    <td class="text-center">{{ item.tong_tieu_thu|dot_format_hide_zero }}</td>
                                    <td class="text-center">{{ item.ton_cuoi_xn1|dot_format_hide_zero }}</td>
                                    <td>{{ item.ghi_chu }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">Không có dữ liệu tồn kho trong khoảng thời gian
                                        này.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="bg-purple text-white">
                                <tr>
                                    <th colspan="7" class="text-end"><strong>TỔNG SỐ LOẠI:</strong></th>
                                    <th class="text-end"><strong>{{ ton_kho_detail_list|length }}</strong></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Khách hàng -->
    <div class="modal fade" id="modalKhachHang" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h4 class="modal-title">Chi tiết Khách hàng từ ngày {{ from_date|date:"d/m/Y" }} đến
                        ngày {{ to_date|date:"d/m/Y" }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                        <table class="table table-bordered table-striped align-middle">
                            <thead class="table-warning sticky-top">
                                <tr>
                                    <th>STT</th>
                                    <th>Hộ khách hàng</th>
                                    <th>Tên thành phẩm</th>
                                    <th>Đơn vị tính</th>
                                    <th>Số lượng tiêu thụ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in khach_hang_detail_list %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ item.ho_khach_hang }}</td>
                                    <td>{{ item.thanh_pham.ten_thanh_pham }}</td>
                                    <td>{{ item.thanh_pham.don_vi_tinh }}</td>
                                    <td class="text-end">{{ item.so_luong|dot_format }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">Không có dữ liệu khách hàng trong khoảng thời
                                        gian này.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-warning">
                                <tr>
                                    <th colspan="4" class="text-end"><strong>TỔNG SỐ KHÁCH HÀNG:</strong></th>
                                    <th class="text-end"><strong>{{ total_khach_hang }}</strong></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Cảnh báo hết hàng -->
    <div class="modal fade" id="modalCanhBao" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h4 class="modal-title">Chi tiết Cảnh báo hết hàng từ ngày {{ from_date|date:"d/m/Y" }} đến
                        ngày {{ to_date|date:"d/m/Y" }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-bordered table-striped">
                            <thead class="table-danger sticky-top">
                                <tr>
                                    <th>STT</th>
                                    <th>Tên thành phẩm</th>
                                    <th>Đơn vị tính</th>
                                    <th>Tồn kho</th>
                                    <th class="text-center">Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in canh_bao_detail %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ item.ten_thanh_pham }}</td>
                                    <td>{{ item.don_vi_tinh }}</td>
                                    <td class="text-end text-danger"><strong>{{ item.ton_cuoi|dot_format }}</strong>
                                    </td>
                                    <td class="text-center">
                                        {% if item.ton_cuoi <= 0 %} <span class="badge bg-danger">Hết hàng</span>
                                            {% elif item.ton_cuoi < 100 %} <span class="badge bg-warning">Rất ít</span>
                                                {% else %}
                                                <span class="badge bg-info">Sắp hết</span>
                                                {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">Không có thành phẩm nào cần cảnh báo</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-danger">
                                <tr>
                                    <th colspan="4" class="text-end"><strong>TỔNG SỐ CẢNH BÁO:</strong></th>
                                    <th class="text-end"><strong>{{ canh_bao_detail|length }}</strong></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <!-- Bootstrap 5.3.7 -->
    <script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>
    <!-- AdminLTE -->
    <script src="{% static 'js/adminlte.min.js' %}"></script>
    <!-- Chart.js Local -->
    <script src="{% static 'js/chart.min.js' %}"></script>

    <script>
        $(document).ready(function () {
            console.log('jQuery loaded:', typeof $ !== 'undefined');
            console.log('Bootstrap loaded:', typeof $.fn.modal !== 'undefined');
            console.log('San xuat records:', {{ san_xuat_detail| length }});
        console.log('Tieu thu records:', {{ tieu_thu_detail| length }});

        // Modal open functionality
        $('[data-toggle="modal"]').on('click', function (e) {
            e.preventDefault();
            var target = $(this).attr('data-target');
            console.log('Opening modal:', target);
            $(target).modal('show');
        });

        // Close button functionality
        $('[data-dismiss="modal"]').on('click', function (e) {
            e.preventDefault();
            console.log('Close button clicked');
            $(this).closest('.modal').modal('hide');
        });

        // ESC key functionality
        $(document).on('keydown', function (e) {
            if (e.keyCode === 27) {
                $('.modal.show').modal('hide');
            }
        });
        });
    </script>

    <!-- Charts JavaScript -->
    <script>
        // Biểu đồ Tiêu thụ thành phẩm
        const ctxTieuThu = document.getElementById('chartTieuThu').getContext('2d');
        const chartTieuThu = new Chart(ctxTieuThu, {
            type: 'bar',
            data: {
                labels: {{ chart_tieu_thu_labels| safe }},
        datasets: [{
            label: 'Số lượng tiêu thụ (kg)',
            data: {{ chart_tieu_thu_data| safe }},
            backgroundColor: [
            'rgba(40, 167, 69, 0.8)',
            'rgba(23, 162, 184, 0.8)',
            'rgba(255, 193, 7, 0.8)',
            'rgba(220, 53, 69, 0.8)',
            'rgba(108, 117, 125, 0.8)',
            'rgba(102, 16, 242, 0.8)',
            'rgba(255, 99, 132, 0.8)',
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 205, 86, 0.8)',
            'rgba(75, 192, 192, 0.8)'
        ],
            borderColor: [
            'rgba(40, 167, 69, 1)',
            'rgba(23, 162, 184, 1)',
            'rgba(255, 193, 7, 1)',
            'rgba(220, 53, 69, 1)',
            'rgba(108, 117, 125, 1)',
            'rgba(102, 16, 242, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 205, 86, 1)',
            'rgba(75, 192, 192, 1)'
        ],
            borderWidth: 1
                }]
            },
        options: {
            responsive: true,
                maintainAspectRatio: false,
                    scales: {
                y: {
                    beginAtZero: true,
                        title: {
                        display: true,
                            text: 'Số lượng (kg)'
                    }
                },
                x: {
                    title: {
                        display: true,
                            text: 'Tên thành phẩm'
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                        position: 'top'
                },
                title: {
                    display: true,
                        text: 'Biểu đồ Tiêu thụ thành phẩm'
                }
            }
        }
        });

        // Biểu đồ Hộ khách hàng tiêu thụ
        const ctxKhachHang = document.getElementById('chartKhachHang').getContext('2d');
        const chartKhachHang = new Chart(ctxKhachHang, {
            type: 'pie',
            data: {
                labels: {{ chart_khach_hang_labels| safe }},
        datasets: [{
            label: 'Tổng số lượng tiêu thụ (kg)',
            data: {{ chart_khach_hang_data| safe }},
            backgroundColor: [
            'rgba(255, 99, 132, 0.8)',
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 205, 86, 0.8)',
            'rgba(75, 192, 192, 0.8)',
            'rgba(153, 102, 255, 0.8)',
            'rgba(255, 159, 64, 0.8)',
            'rgba(199, 199, 199, 0.8)',
            'rgba(83, 102, 255, 0.8)'
        ],
            borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 205, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(199, 199, 199, 1)',
            'rgba(83, 102, 255, 1)'
        ],
            borderWidth: 1
                }]
            },
        options: {
            responsive: true,
                maintainAspectRatio: false,
                    plugins: {
                legend: {
                    display: true,
                        position: 'right'
                },
                title: {
                    display: true,
                        text: 'Biểu đồ Hộ khách hàng tiêu thụ'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return label + ': ' + value.toLocaleString() + ' kg (' + percentage + '%)';
                        }
                    }
                }
            }
        }
        });
    </script>

</body>

</html>