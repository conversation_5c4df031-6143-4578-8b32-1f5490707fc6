#!/usr/bin/env python
"""
Script đơn giản sửa lỗi migration cho máy Admin
"""
import sqlite3
import os
import subprocess

def reset_admin_migrations():
    """Reset migrations cho máy Admin"""
    print("🔄 RESET MIGRATIONS CHO MÁY ADMIN")
    print("=" * 40)
    
    if not os.path.exists("db.sqlite3"):
        print("❌ Không tìm thấy db.sqlite3")
        return False
    
    conn = sqlite3.connect("db.sqlite3")
    cursor = conn.cursor()
    
    try:
        # Xóa tất cả migration z115_app
        cursor.execute("DELETE FROM django_migrations WHERE app = 'z115_app';")
        print("✅ Xóa tất cả migration z115_app khỏi database")
        
        # Thêm lại migration theo đúng thứ tự (chỉ đến 0015)
        migrations = [
            '0001_initial',
            '0002_userprofile', 
            '0003_baokiemvattu',
            '0004_baokiemvattu_ket_qua_kiem_tra_and_more',
            '0005_baokiemvattu_backup_date',
            '0006_baokiembackup_remove_baokiemvattu_backup_date',
            '0007_alter_baokiembackup_options_and_more',
            '0008_alter_dummy_options',
            '0009_alter_baokiembackup_dung_vao_viec_and_more',
            '0010_tonkhob3_tonkhob3backup',
            '0011_phieu_logduyet_phieuvattu_usergroup',
            '0012_phieu_tai_khoan_tao',
            '0013_alter_phieu_trang_thai',
            '0014_danhmucvattu_nhapvattu_xuatvattu',
            '0015_danhmucthanhpham_nhapvattu_quy_cach_sanxuatthanhpham_and_more'
        ]
        
        for migration in migrations:
            cursor.execute("""
                INSERT INTO django_migrations (app, name, applied) 
                VALUES ('z115_app', ?, datetime('now'));
            """, (migration,))
            print(f"  ✅ {migration}")
        
        conn.commit()
        conn.close()
        
        print("✅ Reset migration table thành công!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        conn.rollback()
        conn.close()
        return False

def test_migrations():
    """Test migrations"""
    print("\n🧪 Test migrations...")
    
    try:
        result = subprocess.run("python manage.py showmigrations z115_app", 
                              shell=True, capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            print("✅ Migration status:")
            for line in result.stdout.strip().split('\n'):
                if line.strip() and ('0014' in line or '0015' in line or 'z115_app' in line):
                    print(f"   {line}")
        else:
            print(f"❌ Lỗi: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Lỗi test: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("🔧 SỬA LỖI MIGRATION CHO MÁY ADMIN")
    print("=" * 50)
    
    # Kiểm tra môi trường
    if not os.path.exists("manage.py"):
        print("❌ Không tìm thấy manage.py")
        print("📝 Hãy chuyển đến thư mục z115_project")
        return
    
    current_dir = os.getcwd()
    print(f"📁 Thư mục: {current_dir}")
    
    # Reset migrations
    if reset_admin_migrations():
        # Test migrations
        if test_migrations():
            print("\n" + "=" * 50)
            print("✅ SỬA LỖI THÀNH CÔNG!")
            print("\n📝 BƯỚC TIẾP THEO:")
            print("1. python manage.py runserver 192.170.5.186:8000")
            print("2. Test: http://192.170.5.186:8000/thanh-pham/")
            print("3. Kiểm tra tất cả chức năng")
        else:
            print("\n⚠️ Reset thành công nhưng test có vấn đề")
    else:
        print("\n❌ SỬA LỖI THẤT BẠI!")

if __name__ == "__main__":
    main()
