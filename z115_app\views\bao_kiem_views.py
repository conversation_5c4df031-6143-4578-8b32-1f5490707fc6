from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, HttpResponseForbidden, JsonResponse
from django.contrib import messages
from django.core.paginator import Paginator
from ..models import BaoKiemVatTu, BaoKiemBackup
import logging
from datetime import datetime
import pandas as pd
import io
import os

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@login_required
def bao_kiem_vat_tu_kiem_tra(request):
    if request.user.username != 'PHONGB12':
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP MỤC NÀY.")

    # Khởi tạo session cho dữ liệu tạm
    temp_data = request.session.get('temp_baokiem_data', {})

    if request.method == "POST" and request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        if 'update_temp' in request.POST:
            for item_id in request.POST.getlist('item_id'):
                so_hop_cach = request.POST.get(f"so_hop_cach_{item_id}")
                ket_qua = request.POST.get(f"ket_qua_kiem_tra_{item_id}")
                ngay_tra = request.POST.get(f"ngay_tra_ket_qua_{item_id}")
                if so_hop_cach or ket_qua or ngay_tra:  # Chỉ cập nhật nếu có dữ liệu
                    temp_data[item_id] = {
                        'so_hop_cach': so_hop_cach,
                        'ket_qua_kiem_tra': ket_qua,
                        'ngay_tra_ket_qua': ngay_tra
                    }
            request.session['temp_baokiem_data'] = temp_data
            logger.debug(f"Temporary data updated: {temp_data}")
            return JsonResponse({'status': 'success', 'message': 'Dữ liệu đã được lưu tạm!'})
        elif 'save_all' in request.POST:
            saved_items = False
            error_items = []
            for item_id, data in temp_data.items():
                try:
                    item = BaoKiemVatTu.objects.get(id=item_id)
                    so_hop_cach = data.get('so_hop_cach')
                    ket_qua = data.get('ket_qua_kiem_tra')
                    ngay_tra = data.get('ngay_tra_ket_qua')
                    if so_hop_cach:
                        item.so_hop_cach = int(float(so_hop_cach))
                    if ket_qua:
                        item.ket_qua_kiem_tra = ket_qua
                    if ngay_tra:
                        item.ngay_tra_ket_qua = datetime.strptime(ngay_tra, '%Y-%m-%d').date()
                    item.save()
                    saved_items = True
                    logger.debug(f"Item {item_id} saved successfully with available data.")
                except Exception as e:
                    error_items.append(item_id)
                    logger.error(f"Error saving item {item_id}: {e}")
            
            if saved_items:
                request.session['temp_baokiem_data'] = {}
                return JsonResponse({'status': 'success', 'message': 'Dữ liệu đã được lưu thành công!'})
            else:
                return JsonResponse({'status': 'error', 'message': 'Không có dữ liệu nào được lưu!'})

    # Lọc dữ liệu chưa kiểm (ket_qua_kiem_tra rỗng hoặc null)
    baokiem_list = BaoKiemVatTu.objects.filter(ket_qua_kiem_tra__isnull=True).order_by('-ngay')
    if not baokiem_list.exists():
        baokiem_list = BaoKiemVatTu.objects.filter(ket_qua_kiem_tra='').order_by('-ngay')
    
    paginator = Paginator(baokiem_list, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    return render(request, 'z115_app/bao_kiem_vat_tu_kiem_tra.html', {'page_obj': page_obj})

@login_required
def bao_kiem_vat_tu_da_kiem(request):
    if request.user.username != 'PHONGB12':
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP MỤC NÀY.")
    if request.method == "POST":
        if 'edit_item' in request.POST:
            pass
        elif 'save_item' in request.POST:
            item_id = request.POST.get('save_item')
            item = BaoKiemVatTu.objects.get(id=item_id)
            so_hop_cach = request.POST.get(f"so_hop_cach_{item_id}")
            ket_qua = request.POST.get(f"ket_qua_kiem_tra_{item_id}")
            ngay_tra = request.POST.get(f"ngay_tra_ket_qua_{item_id}")
            try:
                if so_hop_cach:
                    item.so_hop_cach = int(float(so_hop_cach))
                else:
                    item.so_hop_cach = 0
                item.ket_qua_kiem_tra = ket_qua if ket_qua else ''
                item.ngay_tra_ket_qua = datetime.strptime(ngay_tra, '%Y-%m-%d').date() if ngay_tra else None
                item.save()
                messages.success(request, "Cập nhật thành công!")
            except Exception as e:
                messages.error(request, f"Lỗi: {e}")
            return redirect('bao_kiem_vat_tu_da_kiem')
    
    # Lọc dữ liệu đã kiểm (ket_qua_kiem_tra có giá trị)
    baokiem_list = BaoKiemVatTu.objects.exclude(ket_qua_kiem_tra__isnull=True).exclude(ket_qua_kiem_tra='').order_by('-ngay')
    paginator = Paginator(baokiem_list, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    return render(request, 'z115_app/bao_kiem_vat_tu_da_kiem.html', {'page_obj': page_obj})

@login_required
def bao_kiem_vat_tu(request):
    try:
        if request.method == "POST":
            # Debug POST data
            print(f"POST data: {request.POST}")
            print(f"FILES: {request.FILES}")
            print(f"User: {request.user.username}")
            
            # Kiểm tra nếu là yêu cầu AJAX
            is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

            # Xử lý SỬA và XÓA dữ liệu cho PHUONGB3
            if request.POST.get('action') in ['edit_item', 'delete_item'] and request.user.username == 'PHUONGB3':
                phuongb3_password = request.POST.get('phuongb3_password', '')
                if phuongb3_password != '123456':
                    if is_ajax:
                        return JsonResponse({'success': False, 'message': 'Mật khẩu không đúng!'}, status=403)
                    messages.error(request, "MẬT KHẨU KHÔNG ĐÚNG!")
                    return redirect('bao_kiem_vat_tu')

                if request.POST.get('action') == 'edit_item':
                    item_id = request.POST.get('item_id')
                    try:
                        item = BaoKiemVatTu.objects.get(id=item_id)
                        
                        # Cập nhật các trường
                        ngay_str = request.POST.get('ngay')
                        if ngay_str:
                            item.ngay = datetime.strptime(ngay_str, '%Y-%m-%d').date()
                        
                        item.ma_vlspp = request.POST.get('ma_vlspp', '')
                        item.ma_kho = request.POST.get('ma_kho', '')
                        item.ten_quy_cach = request.POST.get('ten_quy_cach', '')
                        item.dung_vao_viec = request.POST.get('dung_vao_viec', '')
                        item.don_vi_tinh = request.POST.get('don_vi_tinh', '')
                        
                        so_luong_str = request.POST.get('so_luong')
                        if so_luong_str:
                            item.so_luong = float(so_luong_str)
                        
                        item.nguoi_bao_kiem = request.POST.get('nguoi_bao_kiem', '')
                        item.noi_de_vat_tu = request.POST.get('noi_de_vat_tu', '')
                        
                        item.save()
                        if is_ajax:
                            return JsonResponse({'success': True, 'message': 'Cập nhật thành công!'})
                        messages.success(request, "Cập nhật thành công!")
                        return redirect('bao_kiem_vat_tu')
                    except Exception as e:
                        if is_ajax:
                            return JsonResponse({'success': False, 'message': str(e)}, status=400)
                        messages.error(request, f"Lỗi: {str(e)}")
                        return redirect('bao_kiem_vat_tu')
                
                elif request.POST.get('action') == 'delete_item':
                    item_id = request.POST.get('item_id')
                    try:
                        item = BaoKiemVatTu.objects.get(id=item_id)
                        item.delete()
                        if is_ajax:
                            return JsonResponse({'success': True, 'message': 'Xóa thành công!'})
                        messages.success(request, "Xóa thành công!")
                        return redirect('bao_kiem_vat_tu')
                    except Exception as e:
                        if is_ajax:
                            return JsonResponse({'success': False, 'message': str(e)}, status=400)
                        messages.error(request, f"Lỗi: {str(e)}")
                        return redirect('bao_kiem_vat_tu')

            # Xử lý upload file Excel cho PHUONGB3
            if 'upload' in request.POST and request.user.username == 'PHUONGB3':
                print("Upload condition met!")
                password = request.POST.get('phuongb3_password')
                print(f"Password: {password}")
                
                if password == '123456':
                    excel_file = request.FILES.get('excel_file')
                    print(f"Excel file: {excel_file}")
                    
                    if excel_file:
                        try:
                            print(f"Processing file: {excel_file.name}")
                            df = pd.read_excel(excel_file, engine='openpyxl')
                            
                            print(f"DataFrame shape: {df.shape}")
                            print(f"DataFrame columns: {list(df.columns)}")
                            
                            new_records = 0
                            updated_records = 0
                            skipped_records = 0
                            
                            for index, row in df.iterrows():
                                try:
                                    # Bỏ qua hàng trống
                                    if pd.isna(row.iloc[1]) or str(row.iloc[1]).strip() == '':
                                        continue
                                    
                                    # Lấy dữ liệu từ Excel
                                    ngay_val = row.iloc[0] if len(row) > 0 else None
                                    ma_vlspp = str(row.iloc[1]).strip()
                                    ma_kho = str(row.iloc[2]).strip() if len(row) > 2 else ''
                                    ten_quy_cach = str(row.iloc[3]).strip() if len(row) > 3 else ''
                                    
                                    if not ma_vlspp or ma_vlspp == 'nan':
                                        continue
                                    
                                    # Xử lý ngày từ Excel
                                    if pd.notna(ngay_val):
                                        if isinstance(ngay_val, str):
                                            try:
                                                ngay = datetime.strptime(ngay_val.strip(), '%d/%m/%Y').date()
                                            except:
                                                try:
                                                    ngay = datetime.strptime(ngay_val.strip(), '%Y-%m-%d').date()
                                                except:
                                                    ngay = datetime.now().date()
                                        else:
                                            ngay = pd.to_datetime(ngay_val).date()
                                    else:
                                        ngay = datetime.now().date()
                                    
                                    # Kiểm tra xem bản ghi đã tồn tại chưa (dựa vào ngày, mã VLSPP, tên quy cách)
                                    existing_record = BaoKiemVatTu.objects.filter(
                                        ngay=ngay,
                                        ma_vlspp=ma_vlspp,
                                        ten_quy_cach=ten_quy_cach
                                    ).first()
                                    
                                    if existing_record:
                                        # Nếu đã tồn tại, cập nhật thông tin (trừ kết quả kiểm tra)
                                        existing_record.ma_kho = ma_kho
                                        existing_record.dung_vao_viec = str(row.iloc[4]).strip() if len(row) > 4 else ''
                                        existing_record.don_vi_tinh = str(row.iloc[5]).strip() if len(row) > 5 else ''
                                        existing_record.so_luong = float(row.iloc[6]) if len(row) > 6 and pd.notna(row.iloc[6]) else 0
                                        existing_record.nguoi_bao_kiem = str(row.iloc[7]).strip() if len(row) > 7 else ''
                                        existing_record.noi_de_vat_tu = str(row.iloc[8]).strip() if len(row) > 8 else ''
                                        # Không cập nhật: so_hop_cach, ket_qua_kiem_tra, ngay_tra_ket_qua, phong_b12_ky, phong_b3_ky
                                        existing_record.save()
                                        updated_records += 1
                                        print(f"Updated existing record: {ma_vlspp} - {ngay}")
                                    else:
                                        # Tạo bản ghi mới
                                        BaoKiemVatTu.objects.create(
                                            ngay=ngay,
                                            ma_vlspp=ma_vlspp,
                                            ma_kho=ma_kho,
                                            ten_quy_cach=ten_quy_cach,
                                            dung_vao_viec=str(row.iloc[4]).strip() if len(row) > 4 else '',
                                            don_vi_tinh=str(row.iloc[5]).strip() if len(row) > 5 else '',
                                            so_luong=float(row.iloc[6]) if len(row) > 6 and pd.notna(row.iloc[6]) else 0,
                                            nguoi_bao_kiem=str(row.iloc[7]).strip() if len(row) > 7 else '',
                                            noi_de_vat_tu=str(row.iloc[8]).strip() if len(row) > 8 else '',
                                            so_hop_cach=0,
                                            ket_qua_kiem_tra='',
                                            ngay_tra_ket_qua=None,
                                            phong_b12_ky='',
                                            phong_b3_ky=''
                                        )
                                        new_records += 1
                                        print(f"Created new record: {ma_vlspp} - {ngay}")
                                    
                                except Exception as row_error:
                                    print(f"Row error: {row_error}")
                                    skipped_records += 1
                                    continue
                            
                            # Thông báo kết quả
                            result_message = f"TẢI LÊN THÀNH CÔNG! "
                            if new_records > 0:
                                result_message += f"Đã thêm {new_records} bản ghi mới. "
                            if updated_records > 0:
                                result_message += f"Đã cập nhật {updated_records} bản ghi. "
                            if skipped_records > 0:
                                result_message += f"Bỏ qua {skipped_records} bản ghi lỗi."
                            
                            print(f"Summary: New={new_records}, Updated={updated_records}, Skipped={skipped_records}")
                            messages.success(request, result_message)
                            
                        except Exception as e:
                            print(f"ERROR: {str(e)}")
                            import traceback
                            print(f"TRACEBACK: {traceback.format_exc()}")
                            messages.error(request, f"LỖI: {str(e)}")
                    else:
                        print("No excel file found")
                        messages.error(request, "VUI LÒNG CHỌN FILE!")
                    return redirect('bao_kiem_vat_tu')
                else:
                    print("Wrong password")
                    messages.error(request, "MẬT KHẨU KHÔNG ĐÚNG!")
                    return redirect('bao_kiem_vat_tu')
            # Xử lý reset dữ liệu cho PHUONGB3
            if request.POST.get('reset_data') and request.user.username == 'PHUONGB3':
                password = request.POST.get('phuongb3_password')
                if password == '123456':
                    BaoKiemVatTu.objects.all().delete()
                    messages.success(request, "ĐÃ XÓA TẤT CẢ DỮ LIỆU!")
                else:
                    messages.error(request, "MẬT KHẨU KHÔNG ĐÚNG!")
                return redirect('bao_kiem_vat_tu')

            # Xử lý backup dữ liệu cho PHUONGB3
            if request.POST.get('backup_data') and request.user.username == 'PHUONGB3':
                password = request.POST.get('phuongb3_password')
                if password == '123456':
                    backup_date = datetime.now()
                    for item in BaoKiemVatTu.objects.all():
                        BaoKiemBackup.objects.create(
                            backup_date=backup_date,
                            ngay=item.ngay,
                            ma_vlspp=item.ma_vlspp,
                            ma_kho=item.ma_kho,
                            ten_quy_cach=item.ten_quy_cach,
                            dung_vao_viec=item.dung_vao_viec,
                            don_vi_tinh=item.don_vi_tinh,
                            so_luong=item.so_luong,
                            nguoi_bao_kiem=item.nguoi_bao_kiem,
                            noi_de_vat_tu=item.noi_de_vat_tu,
                            so_hop_cach=item.so_hop_cach,
                            ket_qua_kiem_tra=item.ket_qua_kiem_tra,
                            ngay_tra_ket_qua=item.ngay_tra_ket_qua,
                            phong_b12_ky=item.phong_b12_ky,
                            phong_b3_ky=item.phong_b3_ky
                        )
                    messages.success(request, f"ĐÃ SAO LƯU DỮ LIỆU NGÀY {backup_date.strftime('%d/%m/%Y %H:%M')}")
                else:
                    messages.error(request, "MẬT KHẨU KHÔNG ĐÚNG!")
                return redirect('bao_kiem_vat_tu')

            # Xử lý khôi phục dữ liệu cho PHUONGB3
            if request.POST.get('restore_data') and request.user.username == 'PHUONGB3':
                password = request.POST.get('phuongb3_password')
                if password == '123456':
                    backup_date_str = request.POST.get('backup_date')
                    if backup_date_str:
                        backup_date = datetime.strptime(backup_date_str, '%Y-%m-%d %H:%M:%S')
                        BaoKiemVatTu.objects.all().delete()
                        for backup in BaoKiemBackup.objects.filter(backup_date=backup_date):
                            BaoKiemVatTu.objects.create(
                                ngay=backup.ngay,
                                ma_vlspp=backup.ma_vlspp,
                                ma_kho=backup.ma_kho,
                                ten_quy_cach=backup.ten_quy_cach,
                                dung_vao_viec=backup.dung_vao_viec,
                                don_vi_tinh=backup.don_vi_tinh,
                                so_luong=backup.so_luong,
                                nguoi_bao_kiem=backup.nguoi_bao_kiem,
                                noi_de_vat_tu=backup.noi_de_vat_tu,
                                so_hop_cach=backup.so_hop_cach,
                                ket_qua_kiem_tra=backup.ket_qua_kiem_tra,
                                ngay_tra_ket_qua=backup.ngay_tra_ket_qua,
                                phong_b12_ky=backup.phong_b12_ky,
                                phong_b3_ky=backup.phong_b3_ky
                            )
                        logger.debug(f"Restored data from backup date {backup_date}")
                        messages.success(request, f"ĐÃ KHÔI PHỤC DỮ LIỆU TỪ NGÀY SAO LƯU {backup_date}")
                    else:
                        messages.error(request, "VUI LÒNG CHỌN NGÀY SAO LƯU!")
                    return redirect('bao_kiem_vat_tu')
                else:
                    logger.debug("Password authentication failed")
                    messages.error(request, "MẬT KHẨU PHUONGB3 KHÔNG ĐÚNG!")
                    return redirect('bao_kiem_vat_tu')

            # Nếu không phải PHUONGB3, trả về lỗi quyền truy cập
            if is_ajax:
                return JsonResponse({'success': False, 'message': 'Bạn không có quyền truy cập!'}, status=403)
            return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP MỤC NÀY.")

        baokiem_list = BaoKiemVatTu.objects.all().order_by('-ngay')
        logger.debug(f"Initial baokiem_list count: {baokiem_list.count()}")
        from_date = request.GET.get('from_date')
        to_date = request.GET.get('to_date')
        search_name = request.GET.get('search_name', '').strip()
        search_kho = request.GET.get('search_kho', '').strip()
        filter_type = request.GET.get('filter_type', 'all')

        if from_date and to_date:
            from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
            to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()
            baokiem_list = baokiem_list.filter(ngay__range=[from_date_obj, to_date_obj])
            logger.debug(f"Filtered by date range: {baokiem_list.count()}")

        if search_name:
            baokiem_list = baokiem_list.filter(ten_quy_cach__icontains=search_name)
            logger.debug(f"Filtered by name: {baokiem_list.count()}")

        if search_kho:
            baokiem_list = baokiem_list.filter(ma_kho__icontains=search_kho)
            logger.debug(f"Filtered by kho: {baokiem_list.count()}")

        # Thay đổi logic lọc dựa vào ket_qua_kiem_tra thay vì so_hop_cach
        if filter_type == 'checked':
            baokiem_list = baokiem_list.exclude(ket_qua_kiem_tra__isnull=True).exclude(ket_qua_kiem_tra='')
        elif filter_type == 'unchecked':
            baokiem_list = baokiem_list.filter(ket_qua_kiem_tra__isnull=True) | baokiem_list.filter(ket_qua_kiem_tra='')

        logger.debug(f"Final filtered baokiem_list count: {baokiem_list.count()}")

        paginator = Paginator(baokiem_list, 10)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        backup_dates = None
        if request.user.username == 'PHUONGB3':
            backup_dates = BaoKiemBackup.objects.values_list('backup_date', flat=True).distinct().order_by('-backup_date')

        if request.GET.get('export_excel'):
            output = io.BytesIO()
            df = pd.DataFrame(list(baokiem_list.values(
                'ngay', 'ma_vlspp', 'ma_kho', 'ten_quy_cach', 'dung_vao_viec', 'don_vi_tinh',
                'so_luong', 'nguoi_bao_kiem', 'noi_de_vat_tu', 'so_hop_cach', 'ket_qua_kiem_tra',
                'ngay_tra_ket_qua', 'phong_b12_ky', 'phong_b3_ky'
            )))
            df['ngay'] = df['ngay'].astype(str)
            df['ngay_tra_ket_qua'] = df['ngay_tra_ket_qua'].astype(str)
            df.to_excel(output, index=False)
            output.seek(0)
            response = HttpResponse(output.getvalue(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = 'attachment; filename="bao_kiem_vat_tu.xlsx"'
            return response

        return render(request, 'z115_app/bao_kiem_vat_tu.html', {
            'page_obj': page_obj,
            'from_date': from_date,
            'to_date': to_date,
            'search_name': search_name,
            'search_kho': search_kho,
            'filter_type': filter_type,
            'backup_dates': backup_dates if request.user.username == 'PHUONGB3' else None
        })
    except Exception as e:
        logger.error(f"Unexpected error in bao_kiem_vat_tu: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'Lỗi hệ thống, vui lòng thử lại sau!'}, status=500)
        messages.error(request, "Lỗi hệ thống, vui lòng thử lại sau!")
        return redirect('bao_kiem_vat_tu')