#!/usr/bin/env python
"""
Script sửa nhanh bảng thiếu cho máy <PERSON>
"""
import sqlite3

def fix_tables():
    """Sửa nhanh bảng thiếu"""
    print("⚡ SỬA NHANH BẢNG THIẾU")
    print("=" * 25)
    
    conn = sqlite3.connect("db.sqlite3")
    cursor = conn.cursor()
    
    try:
        # Tạo bảng SanXuatThanhPham (bảng quan trọng nhất)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "z115_app_sanxuatthanhpham" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "ngay_san_xuat" date NOT NULL,
                "so_luong" real NOT NULL,
                "phan_xuong_san_xuat" varchar(255) NOT NULL,
                "ghi_chu" varchar(255) NOT NULL,
                "thanh_pham_id" bigint NOT NULL
            );
        """)
        print("✅ z115_app_sanxuatthanhpham")

        # Tạo bảng TieuThuThanhPham
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "z115_app_tieuthuthanhpham" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "ngay_tieu_thu" date NOT NULL,
                "so_luong" real NOT NULL,
                "ho_khach_hang" varchar(255) NOT NULL,
                "ghi_chu" varchar(255) NOT NULL,
                "thanh_pham_id" bigint NOT NULL
            );
        """)
        print("✅ z115_app_tieuthuthanhpham")

        # Tạo bảng SanXuatTNCN
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "z115_app_sanxuattncn" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "ngay_san_xuat" date NOT NULL,
                "so_luong" real NOT NULL,
                "phan_xuong_san_xuat" varchar(255) NOT NULL,
                "ghi_chu" varchar(255) NOT NULL,
                "thanh_pham_id" bigint NOT NULL
            );
        """)
        print("✅ z115_app_sanxuattncn")

        # Tạo bảng TieuThuTNCN
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "z115_app_tieuthutncn" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "ngay_tieu_thu" date NOT NULL,
                "so_luong" real NOT NULL,
                "ho_khach_hang" varchar(255) NOT NULL,
                "ghi_chu" varchar(255) NOT NULL,
                "thanh_pham_id" bigint NOT NULL
            );
        """)
        print("✅ z115_app_tieuthutncn")

        # Tạo bảng TonKhoKZ24
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "z115_app_tonkhokz24" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "ngay" date NOT NULL,
                "so_luong" real NOT NULL,
                "ghi_chu" varchar(255) NOT NULL,
                "vat_tu_id" bigint NOT NULL
            );
        """)
        print("✅ z115_app_tonkhokz24")

        conn.commit()
        conn.close()
        
        print("\n✅ HOÀN THÀNH!")
        print("🚀 Restart server và test:")
        print("http://192.170.5.186:8000/thanh-pham/")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        conn.rollback()
        conn.close()
        return False

if __name__ == "__main__":
    fix_tables()
