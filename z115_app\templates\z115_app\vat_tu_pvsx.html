{% load static %}
{% load custom_filters %}
{% load humanize %}
<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>VẬT TƯ PVSX | Z115 System</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Bootstrap 5.3.7 -->
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{% static 'css/all.min.css' %}">
    <!-- AdminLTE -->
    <link rel="stylesheet" href="{% static 'css/adminlte.min.css' %}">
    <!-- Chart.js -->
    <script src="{% static 'js/chart.min.js' %}"></script>

    <style>
        /* Global font size 16px */
        body,
        .content-wrapper,
        .small-box,
        .card,
        .table,
        .modal-body,
        .form-control,
        .btn,
        label,
        p,
        h5,
        h6 {
            font-size: 16px !important;
        }

        /* Statistics cards */
        .small-box .inner h3 {
            font-size: 20px !important;
            font-weight: bold;
        }

        .small-box .inner p {
            font-size: 18px !important;
        }

        /* Tăng cỡ chữ cho input date */
        input[type="date"] {
            font-size: 18px !important;
            font-weight: 500 !important;
            padding: 8px 12px !important;
        }

        /* Đảm bảo text trong input date hiển thị đúng cỡ chữ */
        input[type="date"]::-webkit-datetime-edit {
            font-size: 18px !important;
        }

        input[type="date"]::-webkit-datetime-edit-text {
            font-size: 18px !important;
        }

        input[type="date"]::-webkit-datetime-edit-month-field {
            font-size: 18px !important;
        }

        input[type="date"]::-webkit-datetime-edit-day-field {
            font-size: 18px !important;
        }

        input[type="date"]::-webkit-datetime-edit-year-field {
            font-size: 18px !important;
        }

        .small-box-footer {
            font-size: 18px !important;
        }

        /* Tables */
        .table th,
        .table td {
            font-size: 16px !important;
        }

        /* Modal headers */
        .modal-title {
            font-size: 18px !important;
        }

        /* Card titles */
        .card-title {
            font-size: 18px !important;
        }

        /* Badges */
        .badge {
            font-size: 16px !important;
        }

        /* List items */
        li {
            font-size: 16px !important;
        }

        /* Chart containers */
        .chart-container,
        .chart-container * {
            font-size: 16px !important;
        }

        /* Alert boxes */
        .alert,
        .alert * {
            font-size: 16px !important;
        }

        /* Time info text */
        .text-muted {
            font-size: 16px !important;
        }

        .content-wrapper {
            margin-left: 250px;
        }

        .main-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            z-index: 1000;
        }

        .navbar {
            margin-left: 250px;
        }

        .small-box {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .alert-warning {
            background: linear-gradient(45deg, #ff9800, #ffc107);
            color: white;
            border: none;
        }

        .nav-sidebar .nav-link.active {
            background-color: #007bff;
            color: white;
        }

        .small-box-footer {
            position: relative;
            text-align: center;
            padding: 3px 0;
            color: #fff;
            color: rgba(255, 255, 255, 0.8);
            display: block;
            z-index: 10;
            background: rgba(0, 0, 0, 0.1);
            text-decoration: none;
        }

        .small-box-footer:hover {
            color: #fff;
            background: rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }

        .modal-header.bg-info {
            background-color: #17a2b8 !important;
            color: white;
        }

        .modal-header.bg-success {
            background-color: #28a745 !important;
            color: white;
        }

        .modal-header.bg-warning {
            background-color: #ffc107 !important;
            color: #212529;
        }

        .modal-header.bg-danger {
            background-color: #dc3545 !important;
            color: white;
        }

        .badge-info {
            background-color: #17a2b8;
        }

        .badge-success {
            background-color: #28a745;
        }

        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .badge-danger {
            background-color: #dc3545;
        }

        .badge-primary {
            background-color: #007bff;
        }

        /* Font Times New Roman cho toàn bộ dashboard */
        .content-wrapper,
        .card,
        .table,
        .modal,
        .alert,
        .small-box {
            font-family: 'Times New Roman', serif !important;
            font-size: 14px !important;
        }

        /* Sidebar styling */
        .nav-sidebar .nav-link {
            font-family: 'Times New Roman', serif !important;
            font-size: 16px !important;
            color: white !important;
        }

        .nav-sidebar .nav-link.active {
            background-color: #007bff !important;
            color: white !important;
            font-weight: bold !important;
        }

        .sidebar-dark-primary .nav-sidebar>.nav-item>.nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        .sidebar-dark-primary .nav-sidebar>.nav-item>.nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: white !important;
        }

        /* Brand link styling */
        .brand-link {
            font-family: 'Times New Roman', serif !important;
            font-size: 18px !important;
            font-weight: bold !important;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">

        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="{% url 'home' %}" class="nav-link">Trang chủ</a>
                </li>
            </ul>

            <!-- Right navbar links -->
            <ul class="navbar-nav ml-auto">
                <li class="nav-item">
                    <span class="navbar-text">Xin chào, {{ user.username }}</span>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'logout' %}">
                        <i class="fas fa-sign-out-alt"></i> Đăng xuất
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- Brand Logo -->
            <a href="{% url 'vat_tu_pvsx' %}" class="brand-link">
                <i class="fas fa-boxes brand-image"></i>
                <span class="brand-text font-weight-light">VẬT TƯ PVSX</span>
            </a>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu"
                        data-accordion="false">
                        <li class="nav-item">
                            <a href="{% url 'vat_tu_pvsx' %}" class="nav-link active">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>Dashboard</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'danh_sach_vat_tu' %}" class="nav-link">
                                <i class="nav-icon fas fa-list"></i>
                                <p>DANH MỤC VẬT TƯ</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'nhap_vat_tu' %}" class="nav-link">
                                <i class="nav-icon fas fa-arrow-down"></i>
                                <p>NHẬP VẬT TƯ</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'xuat_vat_tu' %}" class="nav-link">
                                <i class="nav-icon fas fa-arrow-up"></i>
                                <p>XUẤT VẬT TƯ</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'ton_kho_vat_tu' %}" class="nav-link">
                                <i class="nav-icon fas fa-warehouse"></i>
                                <p>TỒN KHO VẬT TƯ</p>
                            </a>
                        </li>
                        {% if user.username == "PHANXUONGA6" or user.username == "hungpc892" %}
                        <li class="nav-item">
                            <a href="{% url 'ton_kho_kz24' %}" class="nav-link">
                                <i class="nav-icon fas fa-archive"></i>
                                <p>TỒN KHO KZ24 A6</p>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Content Header -->
            <div class="content-header">
                <div class="container-fluid">
                    <div class="row mb-3">
                        <div class="col-12 text-center">
                            <h1 class="m-0 font-weight-bold text-primary">TỒN KHO VẬT TƯ PVSX XÍ NGHIỆP I</h1>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    <!-- Date Filter -->
                    <div class="row mb-4 justify-content-center">
                        <div class="col-12 text-center">
                            <form method="GET" class="form-inline justify-content-center" style="font-size: 16px;">
                                <div class="form-group mr-3">
                                    <label for="from_date" class="mr-2 font-weight-bold" style="color: #007bff;">TỪ
                                        NGÀY:</label>
                                    <input type="date" class="form-control" id="from_date" name="from_date"
                                        value="{{ from_date_str }}" style="font-size: 18px;">
                                </div>
                                <div class="form-group mr-3">
                                    <label for="to_date" class="mr-2 font-weight-bold" style="color: #007bff;">ĐẾN
                                        NGÀY:</label>
                                    <input type="date" class="form-control" id="to_date" name="to_date"
                                        value="{{ to_date_str }}" style="font-size: 18px;">
                                </div>
                                <button type="submit" class="btn btn-primary" style="font-size: 16px;">
                                    <i class="fas fa-search"></i> LỌC
                                </button>
                            </form>
                        </div>
                    </div>



                    <!-- Statistics Cards -->
                    <div class="row justify-content-center mx-2">
                        <div class="col-lg col-md-4 col-sm-6 col-12 mb-3 px-2">
                            <div class="small-box bg-info">
                                <div class="inner text-center">
                                    <h3>{{ total_nhap|dot_format }}</h3>
                                    <p>Tổng nhập trong kỳ</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <a href="#" class="small-box-footer text-center" data-toggle="modal"
                                    data-target="#modalTongNhap">
                                    <i class="fas fa-eye"></i> XEM
                                </a>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6 col-12 mb-3 px-2">
                            <div class="small-box bg-success">
                                <div class="inner text-center">
                                    <h3>{{ total_xuat|dot_format }}</h3>
                                    <p>Tổng xuất trong kỳ</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                                <a href="#" class="small-box-footer text-center" data-toggle="modal"
                                    data-target="#modalTongXuat">
                                    <i class="fas fa-eye"></i> XEM
                                </a>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6 col-12 mb-3 px-2">
                            <div class="small-box bg-purple">
                                <div class="inner text-center">
                                    <h3>{{ total_ton_kho }}</h3>
                                    <p>Tồn kho trong kỳ</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-warehouse"></i>
                                </div>
                                <a href="#" class="small-box-footer text-center" data-toggle="modal"
                                    data-target="#modalTonKho">
                                    <i class="fas fa-eye"></i> XEM
                                </a>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6 col-12 mb-3 px-2">
                            <div class="small-box bg-warning">
                                <div class="inner text-center">
                                    <h3>{{ total_nha_cung_cap }}</h3>
                                    <p>Nhà cung cấp</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <a href="#" class="small-box-footer text-center" data-toggle="modal"
                                    data-target="#modalNhaCungCap">
                                    <i class="fas fa-eye"></i> XEM
                                </a>
                            </div>
                        </div>
                        <div class="col-lg col-md-4 col-sm-6 col-12 mb-3 px-2">
                            <div class="small-box bg-danger">
                                <div class="inner text-center">
                                    <h3>{{ canh_bao_het_hang|length }}</h3>
                                    <p>Cảnh báo hết hàng</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <a href="#" class="small-box-footer text-center" data-toggle="modal"
                                    data-target="#modalCanhBao">
                                    <i class="fas fa-eye"></i> XEM
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Information -->
                    <div class="row">
                        <!-- Nhập vật tư -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="fas fa-arrow-down text-info"></i> Nhập vật tư trong kỳ
                                    </h3>
                                </div>
                                <div class="card-body">
                                    {% if nha_cung_cap_stats %}
                                    {% for ncc, items in nha_cung_cap_stats.items %}
                                    <div class="mb-4">
                                        <h5 class="text-info">
                                            <i class="fas fa-truck"></i> {{ ncc }}
                                        </h5>
                                        <p class="text-muted mb-2" style="font-size: 14px;">
                                            <strong>Từ ngày {{ from_date|date:"d/m/Y" }} đến {{ to_date|date:"d/m/Y" }}
                                                - Chi tiết {{ items|length }} loại vật tư</strong>
                                        </p>

                                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                            <table class="table table-bordered table-striped table-sm">
                                                <thead class="table-info sticky-top">
                                                    <tr>
                                                        <th>STT</th>
                                                        <th>Vật tư</th>
                                                        <th>Số lượng</th>
                                                        <th>Ngày nhập</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for item in items %}
                                                    <tr>
                                                        <td class="text-center">{{ forloop.counter }}</td>
                                                        <td>{{ item.ten_vat_tu }}</td>
                                                        <td class="text-center">{{ item.so_luong|dot_format }}</td>
                                                        <td class="text-center">{{ item.ngay_nhap|date:"d/m/Y" }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    {% endfor %}
                                    {% else %}
                                    <p class="text-muted">Không có dữ liệu nhập vật tư trong kỳ này.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Thống kê chi tiết theo từng loại vật tư -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="fas fa-chart-line text-primary"></i> Thống kê chi tiết theo từng loại
                                        vật tư
                                    </h3>
                                </div>
                                <div class="card-body">
                                    {% if vat_tu_stats %}
                                    {% for ten_vt, data in vat_tu_stats.items %}
                                    <div class="mb-4">
                                        <h5 class="text-primary">
                                            <i class="fas fa-box"></i> {{ ten_vt }}
                                            <span class="badge badge-primary float-right">
                                                Tổng: {{ data.tong_so_luong|dot_format }} {{ data.don_vi_tinh }}
                                            </span>
                                        </h5>
                                        <p class="text-muted mb-2" style="font-size: 14px;">
                                            <strong>Từ ngày {{ from_date|date:"d/m/Y" }} đến {{ to_date|date:"d/m/Y" }}
                                                - Chi tiết {{ data.nha_cung_cap|length }} nhà cung cấp</strong>
                                        </p>

                                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                            <table class="table table-bordered table-striped table-sm">
                                                <thead class="table-primary sticky-top">
                                                    <tr>
                                                        <th>STT</th>
                                                        <th>Nhà cung cấp</th>
                                                        <th>Số lượng</th>
                                                        <th>Ngày nhập</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for ncc, nhap_list in data.nha_cung_cap.items %}
                                                    {% for nhap in nhap_list %}
                                                    <tr>
                                                        <td class="text-center">{{ forloop.parentloop.counter }}</td>
                                                        <td>{{ ncc }}</td>
                                                        <td class="text-center">{{ nhap.so_luong|dot_format }}</td>
                                                        <td class="text-center">{{ nhap.ngay_nhap|date:"d/m/Y" }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    {% endfor %}
                                    {% else %}
                                    <p class="text-muted">Không có dữ liệu nhập vật tư trong kỳ này.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="fas fa-chart-bar"></i> Biểu đồ nhập vật tư
                                    </h3>
                                </div>
                                <div class="card-body">
                                    <canvas id="nhapChart" style="height: 300px;"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="fas fa-chart-pie"></i> Biểu đồ xuất vật tư
                                    </h3>
                                </div>
                                <div class="card-body">
                                    <canvas id="xuatChart" style="height: 300px;"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Alert for low stock -->
                    {% if canh_bao_het_hang %}
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <h5><i class="icon fas fa-exclamation-triangle"></i> Cảnh báo vật tư sắp hết!</h5>
                                <ul class="mb-0">
                                    {% for item in canh_bao_het_hang %}
                                    <li>{{ item.ten_vat_tu }}: còn {{ item.ton_kho|dot_format }} {{ item.don_vi_tinh }}
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                    {% endif %}


                </div>
            </section>
        </div>

        <!-- Footer -->
        <footer class="main-footer" style="font-family: 'Times New Roman', serif; font-size: 14px;">
            <strong>@ 2025 copyright by Trần Đình Hưng - Phòng Vật tư - Nhà máy Z115 - Tổng cục CNQP</strong>
        </footer>
    </div>

    <!-- jQuery -->
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <!-- Bootstrap 4 -->
    <script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>
    <!-- AdminLTE App -->
    <script src="{% static 'js/adminlte.min.js' %}"></script>

    <!-- Modal functionality script -->
    <script>
        $(document).ready(function () {
            console.log('jQuery loaded:', typeof $ !== 'undefined');
            console.log('Bootstrap loaded:', typeof $.fn.modal !== 'undefined');
            console.log('Total nhap records:', {{ nhap_today| length }});
        console.log('Total xuat records:', {{ xuat_today| length }});

        // Ensure modal functionality works
        $('[data-toggle="modal"]').on('click', function (e) {
            e.preventDefault();
            var target = $(this).attr('data-target');
            console.log('Opening modal:', target);
            $(target).modal('show');
        });

        // Alternative click handler for small-box-footer
        $('.small-box-footer').on('click', function (e) {
            e.preventDefault();
            var target = $(this).attr('data-target');
            console.log('Small box clicked, target:', target);
            if (target) {
                $(target).modal('show');
            }
        });

        // Ensure close button works
        $('[data-dismiss="modal"]').on('click', function () {
            $(this).closest('.modal').modal('hide');
        });

        // Close modal when clicking outside
        $('.modal').on('click', function (e) {
            if (e.target === this) {
                $(this).modal('hide');
            }
        });
        });
    </script>

    <script>
        // Chart.js configuration
        const nhapCtx = document.getElementById('nhapChart').getContext('2d');
        const xuatCtx = document.getElementById('xuatChart').getContext('2d');

        // Nhập Chart
        new Chart(nhapCtx, {
            type: 'bar',
            data: {
                labels: {{ chart_data.nhap_labels | safe }},
            datasets: [{
                label: 'Số lượng nhập',
                data: {{ chart_data.nhap_data | safe }},
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
            }]
        },
            options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

        // Xuất Chart
        new Chart(xuatCtx, {
            type: 'doughnut',
            data: {
                labels: {{ chart_data.xuat_labels | safe }},
            datasets: [{
                data: {{ chart_data.xuat_data | safe }},
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40',
                '#FF6384',
                '#C9CBCF',
                '#4BC0C0',
                '#FF6384'
            ]
            }]
        },
            options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    </script>

    <!-- Modal Tổng Nhập -->
    <div class="modal fade" id="modalTongNhap" tabindex="-1" role="dialog" aria-labelledby="modalTongNhapLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header bg-info">
                    <h4 class="modal-title" id="modalTongNhapLabel">
                        <i class="fas fa-arrow-down"></i> Chi tiết tổng nhập vật tư từ ngày {{ from_date|date:"d/m/Y" }}
                        đến {{ to_date|date:"d/m/Y" }}
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-bordered table-striped table-sm">
                            <thead class="table-info sticky-top">
                                <tr>
                                    <th>STT</th>
                                    <th>Tên vật tư</th>
                                    <th>Đơn vị tính</th>
                                    <th class="text-center">Số lượng</th>
                                    <th>Nhà cung cấp</th>
                                    <th>Ngày nhập</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in nhap_today %}
                                <tr>
                                    <td class="text-center">{{ forloop.counter }}</td>
                                    <td>{{ item.vat_tu.ten_vat_tu }}</td>
                                    <td class="text-center">{{ item.vat_tu.don_vi_tinh }}</td>
                                    <td class="text-center">{{ item.so_luong|dot_format }}</td>
                                    <td>{{ item.nha_cung_cap|default:"Không xác định" }}</td>
                                    <td class="text-center">{{ item.ngay_nhap|date:"d/m/Y" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        Không có dữ liệu nhập vật tư trong kỳ này ({{ from_date }} - {{ to_date }})
                                        <br><small>Tổng records: {{ nhap_today|length }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-info">
                                <tr>
                                    <th colspan="3" class="text-center"><strong>TỔNG CỘNG</strong></th>
                                    <th class="text-center"><strong>{{ total_nhap|dot_format }}</strong></th>
                                    <th colspan="2"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Tổng Xuất -->
    <div class="modal fade" id="modalTongXuat" tabindex="-1" role="dialog" aria-labelledby="modalTongXuatLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header bg-success">
                    <h4 class="modal-title" id="modalTongXuatLabel">
                        <i class="fas fa-arrow-up"></i> Chi tiết tổng xuất vật tư từ ngày {{ from_date|date:"d/m/Y" }}
                        đến {{ to_date|date:"d/m/Y" }}
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-bordered table-striped table-sm">
                            <thead class="table-success sticky-top">
                                <tr>
                                    <th>STT</th>
                                    <th>Tên vật tư</th>
                                    <th>Đơn vị tính</th>
                                    <th class="text-center">Số lượng</th>
                                    <th>Ghi chú (Đơn vị nhận)</th>
                                    <th>Ngày xuất</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in xuat_today %}
                                <tr>
                                    <td class="text-center">{{ forloop.counter }}</td>
                                    <td>{{ item.vat_tu.ten_vat_tu }}</td>
                                    <td class="text-center">{{ item.vat_tu.don_vi_tinh }}</td>
                                    <td class="text-center">{{ item.so_luong|dot_format }}</td>
                                    <td>{{ item.ghi_chu|default:"Không xác định" }}</td>
                                    <td class="text-center">{{ item.ngay_xuat|date:"d/m/Y" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">Không có dữ liệu xuất vật tư trong kỳ
                                        này</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-success">
                                <tr>
                                    <th colspan="3" class="text-center"><strong>TỔNG CỘNG</strong></th>
                                    <th class="text-center"><strong>{{ total_xuat|dot_format }}</strong></th>
                                    <th colspan="2"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Nhà Cung Cấp -->
    <div class="modal fade" id="modalNhaCungCap" tabindex="-1" role="dialog" aria-labelledby="modalNhaCungCapLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header bg-warning">
                    <h4 class="modal-title" id="modalNhaCungCapLabel"><i class="fas fa-truck"></i> Chi tiết nhà cung cấp
                        từ {{ from_date|date:"d/m/Y" }} đến {{ to_date|date:"d/m/Y" }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    {% for ncc, items in nha_cung_cap_stats.items %}
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-building"></i> {{ ncc }}
                                <span class="badge badge-warning float-right">
                                    Tổng:
                                    {% for total_ncc, total_qty in nha_cung_cap_totals.items %}
                                    {% if total_ncc == ncc %}{{ total_qty|dot_format }}{% endif %}
                                    {% endfor %}
                                </span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                <table class="table table-bordered table-striped table-sm">
                                    <thead class="table-warning sticky-top">
                                        <tr>
                                            <th>STT</th>
                                            <th>Tên vật tư</th>
                                            <th>Đơn vị tính</th>
                                            <th class="text-center">Số lượng</th>
                                            <th>Ngày nhập</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in items %}
                                        <tr>
                                            <td class="text-center">{{ forloop.counter }}</td>
                                            <td>{{ item.ten_vat_tu }}</td>
                                            <td class="text-center">{{ item.don_vi_tinh }}</td>
                                            <td class="text-center">{{ item.so_luong|dot_format }}</td>
                                            <td class="text-center">{{ item.ngay_nhap|date:"d/m/Y" }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Không có dữ liệu nhà cung cấp trong kỳ này.
                    </div>
                    {% endfor %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Tồn Kho -->
    <div class="modal fade" id="modalTonKho" tabindex="-1" role="dialog" aria-labelledby="modalTonKhoLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header bg-purple">
                    <h4 class="modal-title" id="modalTonKhoLabel"><i class="fas fa-warehouse"></i> Chi tiết tổng tồn kho
                        vật tư từ ngày {{ from_date|date:"d/m/Y" }} đến {{ to_date|date:"d/m/Y" }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-bordered table-striped table-sm">
                            <thead class="table-purple sticky-top">
                                <tr>
                                    <th>STT</th>
                                    <th>Tên vật tư</th>
                                    <th>Đơn vị tính</th>
                                    <th class="text-center">Tồn đầu kỳ ngày {{ from_date|date:"d/m/Y" }}</th>
                                    <th class="text-center">Tổng nhập</th>
                                    <th class="text-center">Tổng xuất</th>
                                    <th class="text-center">Tồn cuối BNV</th>
                                    <th class="text-center">Tồn cuối KZ24</th>
                                    <th class="text-center">Tồn cuối XN1</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in ton_kho_data %}
                                <tr>
                                    <td class="text-center">{{ forloop.counter }}</td>
                                    <td>{{ item.ten_vat_tu }}</td>
                                    <td class="text-center">{{ item.don_vi_tinh }}</td>
                                    <td class="text-center">{{ item.ton_dau|dot_format_hide_zero }}</td>
                                    <td class="text-center">{{ item.tong_nhap|dot_format_hide_zero }}</td>
                                    <td class="text-center">{{ item.tong_xuat|dot_format_hide_zero }}</td>
                                    <td class="text-center">{{ item.ton_cuoi_bnv|dot_format_hide_zero }}</td>
                                    <td class="text-center">{{ item.ton_cuoi_kz24|dot_format_hide_zero }}</td>
                                    <td class="text-center">{{ item.ton_cuoi_xn1|dot_format_hide_zero }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted">Không có dữ liệu tồn kho trong kỳ
                                        này.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Cảnh Báo Hết Hàng -->
    <div class="modal fade" id="modalCanhBao" tabindex="-1" role="dialog" aria-labelledby="modalCanhBaoLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h4 class="modal-title" id="modalCanhBaoLabel"><i class="fas fa-exclamation-triangle"></i> Danh sách
                        vật tư sắp hết hàng đến ngày {{ to_date|date:"d/m/Y" }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-bordered table-striped table-sm">
                            <thead class="table-danger sticky-top">
                                <tr>
                                    <th>STT</th>
                                    <th>Tên vật tư</th>
                                    <th>Đơn vị tính</th>
                                    <th class="text-center">Số lượng tồn kho</th>
                                    <th class="text-center">Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in canh_bao_het_hang %}
                                <tr>
                                    <td class="text-center">{{ forloop.counter }}</td>
                                    <td>{{ item.ten_vat_tu }}</td>
                                    <td class="text-center">{{ item.don_vi_tinh }}</td>
                                    <td class="text-center">{{ item.ton_kho|dot_format }}</td>
                                    <td class="text-center">
                                        {% if item.ton_kho <= 0 %} <span class="badge bg-danger">Hết hàng</span>
                                            {% elif item.ton_kho < 5 %} <span class="badge bg-warning">Rất ít</span>
                                                {% else %}
                                                <span class="badge bg-info">Sắp hết</span>
                                                {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center text-success">
                                        <i class="fas fa-check-circle"></i> Tất cả vật tư đều đủ số lượng tồn kho
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if canh_bao_het_hang %}
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-lightbulb"></i>
                        <strong>Khuyến nghị:</strong> Cần liên hệ nhà cung cấp để đặt hàng bổ sung cho các vật tư trên.
                    </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>

</body>

</html>