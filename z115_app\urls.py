from django.urls import path
from . import views
from .views import bao_kiem_views

urlpatterns = [
    path('', views.home, name='home'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('bao-kiem-vat-tu/', views.bao_kiem_vat_tu, name='bao_kiem_vat_tu'),
    path('bao-kiem-vat-tu-kiem-tra/', views.bao_kiem_vat_tu_kiem_tra, name='bao_kiem_vat_tu_kiem_tra'),
    path('bao-kiem-vat-tu-kiem-tra-locked/', bao_kiem_views.bao_kiem_vat_tu_kiem_tra, name='bao_kiem_vat_tu_kiem_tra_locked'),
    path('bao-kiem-vat-tu-da-kiem/', views.bao_kiem_vat_tu_da_kiem, name='bao_kiem_vat_tu_da_kiem'),
    path('bao-cao-ton-kho-b3/', views.bao_cao_ton_kho_b3, name='bao_cao_ton_kho_b3'),
    path('XNT-kho-xi-nghiep-1/', views.XNT_kho_xi_nghiep_1, name='XNT_kho_xi_nghiep_1'),
    path('XNT-kho-xi-nghiep-1/vat-tu-pvsx/', views.vat_tu_pvsx, name='vat_tu_pvsx'),
    path('XNT-kho-xi-nghiep-1/thanh-pham/', views.thanh_pham, name='thanh_pham'),
    path('cap-vat-tu-khu-a/', views.cap_vat_tu_khu_a, name='cap_vat_tu_khu_a'),
    path('nhap-kho-thanh-pham-khu-a/', views.nhap_kho_thanh_pham_khu_a, name='nhap_kho_thanh_pham_khu_a'),
    path('quan-ly-nguoi-dung/', views.quan_ly_nguoi_dung, name='quan_ly_nguoi_dung'),
    path('tao-phieu/', views.tao_phieu, name='tao_phieu'),
    path('danh-sach-cho-dmv-duyet/', views.danh_sach_cho_dmv_duyet, name='danh_sach_cho_dmv_duyet'),
    path('duyet-phieu-dmv/<int:phieu_id>/', views.danh_sach_cho_dmv_duyet, name='duyet_phieu_dmv'),
    path('danh-sach-cho-ch-duyet/', views.danh_sach_cho_ch_duyet, name='danh_sach_cho_ch_duyet'),
    path('danh-sach-phieu-dmv-duyet/', views.danh_sach_phieu_dmv_duyet, name='danh_sach_phieu_dmv_duyet'),
    path('danh-sach-phieu-ch-duyet/', views.danh_sach_phieu_ch_duyet, name='danh_sach_phieu_ch_duyet'),
    path('quan-ly-nhom-user/', views.quan_ly_nhom_user, name='quan_ly_nhom_user'),
    # Thêm các route mới
    path('get_phieu_details_dmv/<int:phieu_id>/', views.get_phieu_details_dmv, name='get_phieu_details_dmv'),
    path('save_duyet_and_transfer/', views.save_duyet_and_transfer, name='save_duyet_and_transfer'),
    path('save_duyet_ch_ajax/', views.save_duyet_ch_ajax, name='save_duyet_ch_ajax'),
    path('huy_phieu_ch/<int:phieu_id>/', views.huy_phieu_ch, name='huy_phieu_ch'),
    path('get_phieu_details_ch/<int:phieu_id>/', views.get_phieu_details_ch, name='get_phieu_details_ch'),
    path('get_phieu_details_dmv_duyet/<int:phieu_id>/', views.get_phieu_details_dmv_duyet, name='get_phieu_details_dmv_duyet'),
    path('api/phieu_dmv_da_duyet/<int:phieu_id>/', views.chi_tiet_phieu_dmv_da_duyet, name='chi_tiet_phieu_dmv_da_duyet'),
    path('api/phieu_ch_da_duyet/<int:phieu_id>/', views.chi_tiet_phieu_ch_da_duyet, name='chi_tiet_phieu_ch_da_duyet'),
    path('in_phieu/<int:phieu_id>/', views.in_phieu_view, name='in_phieu'),
    path('vat-tu-pvsx/', views.vat_tu_pvsx, name='vat_tu_pvsx'),
    path('vat-tu-pvsx/danh-muc/', views.danh_sach_vat_tu, name='danh_sach_vat_tu'),
    path('vat-tu-pvsx/nhap/', views.nhap_vat_tu, name='nhap_vat_tu'),
    path('vat-tu-pvsx/xuat/', views.xuat_vat_tu, name='xuat_vat_tu'),
    path('vat-tu-pvsx/ton-kho/', views.ton_kho_vat_tu, name='ton_kho_vat_tu'),
    path('vat-tu-pvsx/danh-muc/them/', views.them_vat_tu, name='them_vat_tu'),
    path('vat-tu-pvsx/danh-muc/sua/<int:vat_tu_id>/', views.sua_vat_tu, name='sua_vat_tu'),
    path('vat-tu-pvsx/danh-muc/xoa/<int:vat_tu_id>/', views.xoa_vat_tu, name='xoa_vat_tu'),
    # Thành phẩm
    path('thanh-pham/', views.thanh_pham, name='thanh_pham'),
    path('thanh-pham/danh-muc/', views.danh_muc_thanh_pham, name='danh_muc_thanh_pham'),
    path('thanh-pham/tieu-thu/', views.tieu_thu_tncn, name='tieu_thu_tncn'),
    path('thanh-pham/san-xuat/', views.san_xuat_tncn, name='san_xuat_tncn'),
    path('thanh-pham/ton-kho/', views.ton_kho_tp, name='ton_kho_tp'),

    # Thành phẩm PVSX - URLs mới
    path('thanh-pham/danh-muc-thanh-pham/', views.danh_muc_thanh_pham, name='danh_muc_thanh_pham'),
    path('thanh-pham/san-xuat-thanh-pham/', views.san_xuat_thanh_pham, name='san_xuat_thanh_pham'),
    path('thanh-pham/tieu-thu-thanh-pham/', views.tieu_thu, name='tieu_thu'),
    path('thanh-pham/ton-kho-thanh-pham/', views.ton_kho_thanh_pham, name='ton_kho_thanh_pham'),

    # CRUD Thành phẩm
    path('thanh-pham/them/', views.them_thanh_pham, name='them_thanh_pham'),
    path('thanh-pham/sua/<int:thanh_pham_id>/', views.sua_thanh_pham, name='sua_thanh_pham'),
    path('thanh-pham/xoa/<int:thanh_pham_id>/', views.xoa_thanh_pham, name='xoa_thanh_pham'),

    # CRUD Sản xuất thành phẩm
    path('thanh-pham/san-xuat/them/', views.them_san_xuat_thanh_pham, name='them_san_xuat_thanh_pham'),
    path('thanh-pham/san-xuat/sua/<int:san_xuat_id>/', views.sua_san_xuat_thanh_pham, name='sua_san_xuat_thanh_pham'),
    path('thanh-pham/san-xuat/xoa/<int:san_xuat_id>/', views.xoa_san_xuat_thanh_pham, name='xoa_san_xuat_thanh_pham'),

    # CRUD Tiêu thụ thành phẩm
    path('thanh-pham/tieu-thu/them/', views.them_tieu_thu, name='them_tieu_thu'),
    path('thanh-pham/tieu-thu/sua/<int:tieu_thu_id>/', views.sua_tieu_thu, name='sua_tieu_thu'),
    path('thanh-pham/tieu-thu/xoa/<int:tieu_thu_id>/', views.xoa_tieu_thu, name='xoa_tieu_thu'),

    # API Autocomplete
    path('api/khach-hang-autocomplete/', views.api_khach_hang_autocomplete, name='api_khach_hang_autocomplete'),
    path('api/noi-tieu-thu-autocomplete/', views.api_noi_tieu_thu_autocomplete, name='api_noi_tieu_thu_autocomplete'),

    # CRUD Tồn kho thành phẩm
    path('thanh-pham/ton-kho/sua/<int:ton_kho_id>/', views.sua_ton_kho_thanh_pham, name='sua_ton_kho_thanh_pham'),
    path('thanh-pham/ton-kho/xoa/<int:ton_kho_id>/', views.xoa_ton_kho_thanh_pham, name='xoa_ton_kho_thanh_pham'),
    # Nhập vật tư
    path('vat-tu-pvsx/nhap/them/', views.them_nhap_vat_tu, name='them_nhap_vat_tu'),
    path('vat-tu-pvsx/nhap/sua/<int:nhap_id>/', views.sua_nhap_vat_tu, name='sua_nhap_vat_tu'),
    path('vat-tu-pvsx/nhap/xoa/<int:nhap_id>/', views.xoa_nhap_vat_tu, name='xoa_nhap_vat_tu'),
    # Xuất vật tư
    path('vat-tu-pvsx/xuat/them/', views.them_xuat_vat_tu, name='them_xuat_vat_tu'),
    path('vat-tu-pvsx/xuat/sua/<int:xuat_id>/', views.sua_xuat_vat_tu, name='sua_xuat_vat_tu'),
    path('vat-tu-pvsx/xuat/xoa/<int:xuat_id>/', views.xoa_xuat_vat_tu, name='xoa_xuat_vat_tu'),
    # Tồn kho vật tư (nếu muốn sửa/xoá/thêm thủ công)
    path('vat-tu-pvsx/ton-kho/them/', views.them_ton_kho_vat_tu, name='them_ton_kho_vat_tu'),
    path('vat-tu-pvsx/ton-kho/sua/<int:ton_id>/', views.sua_ton_kho_vat_tu, name='sua_ton_kho_vat_tu'),
    path('vat-tu-pvsx/ton-kho/xoa/<int:ton_id>/', views.xoa_ton_kho_vat_tu, name='xoa_ton_kho_vat_tu'),
    path('vat-tu-pvsx/ton-kho-kz24/', views.ton_kho_kz24, name='ton_kho_kz24'),
    path('vat-tu-pvsx/ton-kho-kz24/sua/<int:ton_id>/', views.sua_ton_kho_kz24, name='sua_ton_kho_kz24'),
    path('vat-tu-pvsx/ton-kho-kz24/xoa/<int:ton_id>/', views.xoa_ton_kho_kz24, name='xoa_ton_kho_kz24'),
]