# Generated by Django 5.0.7 on 2025-08-04 06:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('z115_app', '0017_tonkhokz24'),
    ]

    operations = [
        migrations.AddField(
            model_name='danhmucthanhpham',
            name='phan_xuong_san_xuat',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.CreateModel(
            name='SanXuatThanhPham',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ngay_san_xuat', models.DateField()),
                ('so_luong', models.FloatField()),
                ('phan_xuong_san_xuat', models.CharField(blank=True, max_length=255)),
                ('ghi_chu', models.CharField(blank=True, max_length=255)),
                ('thanh_pham', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='z115_app.danhmucthanhpham')),
            ],
        ),
        migrations.CreateModel(
            name='TieuThuThanhPham',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ngay_tieu_thu', models.DateField()),
                ('so_luong', models.FloatField()),
                ('ho_khach_hang', models.CharField(blank=True, max_length=255)),
                ('noi_tieu_thu', models.CharField(blank=True, max_length=255)),
                ('ghi_chu', models.CharField(blank=True, max_length=255)),
                ('thanh_pham', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='z115_app.danhmucthanhpham')),
            ],
        ),
    ]
