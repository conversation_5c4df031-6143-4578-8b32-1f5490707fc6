#!/usr/bin/env python
"""
Script sửa nhanh cột thiếu cho máy <PERSON>
"""
import sqlite3

def fix_columns():
    """Sửa nhanh cột thiếu"""
    print("⚡ SỬA NHANH CỘT THIẾU")
    print("=" * 25)
    
    conn = sqlite3.connect("db.sqlite3")
    cursor = conn.cursor()
    
    try:
        # Thêm cột phan_xuong_san_xuat vào danhmucthanhpham
        cursor.execute('''
            ALTER TABLE "z115_app_danhmucthanhpham" 
            ADD COLUMN "phan_xuong_san_xuat" varchar(255) DEFAULT '';
        ''')
        print("✅ phan_xuong_san_xuat")
        
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("✅ phan_xuong_san_xuat (đã có)")
        else:
            print(f"❌ Lỗi phan_xuong_san_xuat: {e}")
    
    try:
        # Thêm cột quy_cach vào nhapvattu
        cursor.execute('''
            ALTER TABLE "z115_app_nhapvattu" 
            ADD COLUMN "quy_cach" varchar(255) DEFAULT '';
        ''')
        print("✅ quy_cach")
        
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("✅ quy_cach (đã có)")
        else:
            print(f"❌ Lỗi quy_cach: {e}")
    
    try:
        conn.commit()
        conn.close()
        
        print("\n✅ HOÀN THÀNH!")
        print("🚀 Restart server:")
        print("python manage.py runserver 192.170.5.186:8000")
        print("🌐 Test: http://192.170.5.186:8000/thanh-pham/")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi commit: {e}")
        conn.rollback()
        conn.close()
        return False

if __name__ == "__main__":
    fix_columns()
