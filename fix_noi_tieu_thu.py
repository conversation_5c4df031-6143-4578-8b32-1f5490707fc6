#!/usr/bin/env python
"""
Script sửa nhanh cột noi_tieu_thu thiếu
"""
import sqlite3

def fix_noi_tieu_thu():
    """Sửa nhanh cột noi_tieu_thu thiếu"""
    print("⚡ SỬA NHANH CỘT NOI_TIEU_THU")
    print("=" * 30)
    
    conn = sqlite3.connect("db.sqlite3")
    cursor = conn.cursor()
    
    try:
        # Thêm cột noi_tieu_thu vào tieuthuthanhpham
        cursor.execute('''
            ALTER TABLE "z115_app_tieuthuthanhpham" 
            ADD COLUMN "noi_tieu_thu" varchar(255) DEFAULT '';
        ''')
        print("✅ noi_tieu_thu → z115_app_tieuthuthanhpham")
        
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("✅ noi_tieu_thu (đã có)")
        else:
            print(f"❌ Lỗi noi_tieu_thu: {e}")
    
    try:
        # Thêm cột noi_tieu_thu vào tieuthutncn (phòng trường hợp)
        cursor.execute('''
            ALTER TABLE "z115_app_tieuthutncn" 
            ADD COLUMN "noi_tieu_thu" varchar(255) DEFAULT '';
        ''')
        print("✅ noi_tieu_thu → z115_app_tieuthutncn")
        
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("✅ noi_tieu_thu (đã có)")
        else:
            print(f"❌ Lỗi noi_tieu_thu: {e}")
    
    try:
        conn.commit()
        conn.close()
        
        print("\n✅ HOÀN THÀNH!")
        print("🚀 Restart server:")
        print("python manage.py runserver 192.170.5.186:8000")
        print("🌐 Test: http://192.170.5.186:8000/thanh-pham/")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi commit: {e}")
        conn.rollback()
        conn.close()
        return False

if __name__ == "__main__":
    fix_noi_tieu_thu()
