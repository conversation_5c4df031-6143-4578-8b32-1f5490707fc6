{% extends 'z115_app/base.html' %}
{% load custom_filters %}
{% block title %}TỒN KHO THÀNH PHẨM{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">TỒN KHO THÀNH PHẨM XÍ NGHIỆP I</h2>

    <!-- <PERSON><PERSON> lọc thời gian -->
    <form method="GET" class="row g-3 mb-4">
        <div class="col-md-3">
            <label class="form-label">Từ ngày</label>
            <input type="date" name="from_date" class="form-control" value="{{ from_date }}">
        </div>
        <div class="col-md-3">
            <label class="form-label">Đến ngày</label>
            <input type="date" name="to_date" class="form-control" value="{{ to_date }}">
        </div>
        <div class="col-md-3 align-self-end">
            <button type="submit" class="btn btn-primary">Xem b<PERSON><PERSON> c<PERSON>o</button>
        </div>
    </form>

    <div class="table-responsive">
        <table class="table table-bordered table-striped align-middle">
            <thead class="table-success">
                <tr>
                    <th>STT</th>
                    <th>Tên thành phẩm</th>
                    <th>Đvt</th>
                    <th class="text-center">Tồn đầu kỳ</th>
                    <th class="text-center">Tổng sản xuất</th>
                    <th class="text-center">Tổng tiêu thụ</th>
                    <th class="text-center">Tồn cuối XN1</th>
                    <th>Ghi chú</th>
                    {% if is_quan_ly %}
                    <th>Hành động</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in ton_kho_list %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ item.ten_thanh_pham }}</td>
                    <td>{{ item.don_vi_tinh }}</td>
                    <td class="text-center">{{ item.ton_dau|dot_format_hide_zero }}</td>
                    <td class="text-center">{{ item.tong_san_xuat|dot_format_hide_zero }}</td>
                    <td class="text-center">{{ item.tong_tieu_thu|dot_format_hide_zero }}</td>
                    <td class="text-center">{{ item.ton_cuoi_xn1|dot_format_hide_zero }}</td>
                    <td>{{ item.ghi_chu }}</td>
                    {% if is_quan_ly %}
                    <td>
                        <a href="{% url 'sua_ton_kho_thanh_pham' item.id %}" class="btn btn-warning btn-sm">Sửa</a>
                        <a href="{% url 'xoa_ton_kho_thanh_pham' item.id %}" class="btn btn-danger btn-sm"
                            onclick="return confirm('Bạn chắc chắn muốn xoá?');">Xoá</a>
                    </td>
                    {% endif %}
                </tr>
                {% empty %}
                <tr>
                    <td colspan="{% if is_quan_ly %}9{% else %}8{% endif %}" class="text-center text-muted">
                        Không có dữ liệu tồn kho trong khoảng thời gian này.
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Nút Quay lại -->
        <div class="mt-3">
            <a href="{% url 'thanh_pham' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> QUAY LẠI
            </a>
        </div>
    </div>
</div>
{% endblock %}