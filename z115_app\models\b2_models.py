"""
Models cho PHÒNG TỔ CHỨC LAO ĐỘNG (B2)
<PERSON><PERSON> gồm các models liên quan đến:
- <PERSON><PERSON><PERSON> cá<PERSON> quân số các đơn vị
- Quản lý nhân sự
- B<PERSON>o cáo lao động
"""

from django.db import models
from django.contrib.auth.models import User

# ===== MODELS CHO BÁO CÁO QUÂN SỐ =====
class DonVi(models.Model):
    """Model cho các đơn vị trong công ty"""
    ma_don_vi = models.CharField(max_length=20, unique=True)
    ten_don_vi = models.CharField(max_length=200)
    don_vi_cha = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    mo_ta = models.TextField(blank=True)
    ngay_thanh_lap = models.DateField(null=True, blank=True)
    trang_thai = models.BooleanField(default=True)  # True: hoạt động, False: ngừng hoạt động

    class Meta:
        verbose_name = "Đơn vị"
        verbose_name_plural = "Đơn vị"

    def __str__(self):
        return f"{self.ma_don_vi} - {self.ten_don_vi}"

class ChucVu(models.Model):
    """Model cho các chức vụ"""
    ma_chuc_vu = models.CharField(max_length=20, unique=True)
    ten_chuc_vu = models.CharField(max_length=100)
    cap_bac = models.IntegerField(default=1)  # Cấp bậc chức vụ (1: thấp nhất)
    mo_ta = models.TextField(blank=True)

    class Meta:
        verbose_name = "Chức vụ"
        verbose_name_plural = "Chức vụ"

    def __str__(self):
        return f"{self.ma_chuc_vu} - {self.ten_chuc_vu}"

class NhanVien(models.Model):
    """Model cho nhân viên"""
    GIOI_TINH_CHOICES = [
        ('Nam', 'Nam'),
        ('Nữ', 'Nữ'),
    ]
    
    TRANG_THAI_CHOICES = [
        ('dang_lam', 'Đang làm việc'),
        ('nghi_phep', 'Nghỉ phép'),
        ('nghi_om', 'Nghỉ ốm'),
        ('nghi_thai_san', 'Nghỉ thai sản'),
        ('da_nghi', 'Đã nghỉ việc'),
    ]

    ma_nhan_vien = models.CharField(max_length=20, unique=True)
    ho_ten = models.CharField(max_length=100)
    ngay_sinh = models.DateField()
    gioi_tinh = models.CharField(max_length=10, choices=GIOI_TINH_CHOICES)
    so_cmnd = models.CharField(max_length=20, unique=True)
    dia_chi = models.TextField()
    so_dien_thoai = models.CharField(max_length=15, blank=True)
    email = models.EmailField(blank=True)
    
    # Thông tin công việc
    don_vi = models.ForeignKey(DonVi, on_delete=models.CASCADE)
    chuc_vu = models.ForeignKey(ChucVu, on_delete=models.CASCADE)
    ngay_vao_lam = models.DateField()
    ngay_nghi_viec = models.DateField(null=True, blank=True)
    trang_thai = models.CharField(max_length=20, choices=TRANG_THAI_CHOICES, default='dang_lam')
    
    # Thông tin lương
    luong_co_ban = models.DecimalField(max_digits=12, decimal_places=0, default=0)
    he_so_luong = models.DecimalField(max_digits=5, decimal_places=2, default=1.0)
    
    ghi_chu = models.TextField(blank=True)
    ngay_tao = models.DateTimeField(auto_now_add=True)
    ngay_cap_nhat = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Nhân viên"
        verbose_name_plural = "Nhân viên"

    def __str__(self):
        return f"{self.ma_nhan_vien} - {self.ho_ten}"

    @property
    def tuoi(self):
        from datetime import date
        today = date.today()
        return today.year - self.ngay_sinh.year - ((today.month, today.day) < (self.ngay_sinh.month, self.ngay_sinh.day))

class BaoCaoQuanSo(models.Model):
    """Model cho báo cáo quân số theo tháng"""
    thang = models.IntegerField()
    nam = models.IntegerField()
    don_vi = models.ForeignKey(DonVi, on_delete=models.CASCADE)
    
    # Số liệu quân số
    so_nhan_vien_dau_thang = models.IntegerField(default=0)
    so_nhan_vien_moi = models.IntegerField(default=0)  # Tuyển mới trong tháng
    so_nhan_vien_nghi = models.IntegerField(default=0)  # Nghỉ việc trong tháng
    so_nhan_vien_cuoi_thang = models.IntegerField(default=0)
    
    # Phân loại theo giới tính
    so_nam = models.IntegerField(default=0)
    so_nu = models.IntegerField(default=0)
    
    # Phân loại theo độ tuổi
    so_duoi_30 = models.IntegerField(default=0)
    so_tu_30_den_50 = models.IntegerField(default=0)
    so_tren_50 = models.IntegerField(default=0)
    
    ghi_chu = models.TextField(blank=True)
    ngay_tao = models.DateTimeField(auto_now_add=True)
    nguoi_tao = models.ForeignKey(User, on_delete=models.CASCADE)

    class Meta:
        verbose_name = "Báo cáo quân số"
        verbose_name_plural = "Báo cáo quân số"
        unique_together = ['thang', 'nam', 'don_vi']

    def __str__(self):
        return f"Báo cáo quân số {self.don_vi.ten_don_vi} - {self.thang}/{self.nam}"

class ChiTietQuanSo(models.Model):
    """Model chi tiết quân số theo chức vụ"""
    bao_cao = models.ForeignKey(BaoCaoQuanSo, on_delete=models.CASCADE)
    chuc_vu = models.ForeignKey(ChucVu, on_delete=models.CASCADE)
    so_luong = models.IntegerField(default=0)
    ghi_chu = models.TextField(blank=True)

    class Meta:
        verbose_name = "Chi tiết quân số"
        verbose_name_plural = "Chi tiết quân số"

    def __str__(self):
        return f"{self.bao_cao} - {self.chuc_vu.ten_chuc_vu}: {self.so_luong}"

class LichSuCongTac(models.Model):
    """Model lịch sử công tác của nhân viên"""
    LOAI_THAY_DOI_CHOICES = [
        ('tuyen_moi', 'Tuyển mới'),
        ('chuyen_don_vi', 'Chuyển đơn vị'),
        ('thang_chuc', 'Thăng chức'),
        ('giam_chuc', 'Giáng chức'),
        ('nghi_viec', 'Nghỉ việc'),
        ('quay_lai', 'Quay lại làm việc'),
    ]

    nhan_vien = models.ForeignKey(NhanVien, on_delete=models.CASCADE)
    ngay_thay_doi = models.DateField()
    loai_thay_doi = models.CharField(max_length=20, choices=LOAI_THAY_DOI_CHOICES)
    don_vi_cu = models.ForeignKey(DonVi, on_delete=models.CASCADE, related_name='lich_su_don_vi_cu', null=True, blank=True)
    don_vi_moi = models.ForeignKey(DonVi, on_delete=models.CASCADE, related_name='lich_su_don_vi_moi', null=True, blank=True)
    chuc_vu_cu = models.ForeignKey(ChucVu, on_delete=models.CASCADE, related_name='lich_su_chuc_vu_cu', null=True, blank=True)
    chuc_vu_moi = models.ForeignKey(ChucVu, on_delete=models.CASCADE, related_name='lich_su_chuc_vu_moi', null=True, blank=True)
    ly_do = models.TextField()
    nguoi_thuc_hien = models.ForeignKey(User, on_delete=models.CASCADE)
    ngay_tao = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Lịch sử công tác"
        verbose_name_plural = "Lịch sử công tác"

    def __str__(self):
        return f"{self.nhan_vien.ho_ten} - {self.loai_thay_doi} - {self.ngay_thay_doi}"
