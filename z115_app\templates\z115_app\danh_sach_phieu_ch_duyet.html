{% extends 'z115_app/cap_vat_tu_khu_a.html' %}
{% load static %}

{% block sub_content %}
<div class="container mt-4">
    <h2 class="text-center text-white py-2 mb-4" style="background-color: #6610f2;">
        DANH SÁCH PHIẾU CHỈ HUY ĐÃ DUYỆT
    </h2>

    <!-- FORM LỌC TÌM KIẾM -->
    <form method="get" class="row g-3 mb-4">
        <div class="col-md-4">
            <input type="text" name="search_so_phieu" class="form-control" placeholder="Tìm theo số phiếu..." value="{{ request.GET.search_so_phieu }}">
        </div>
        <div class="col-md-4">
            <input type="text" name="search_don_vi_nhan" class="form-control" placeholder="Tìm theo đơn vị nhận..." value="{{ request.GET.search_don_vi_nhan }}">
        </div>
        <div class="col-md-4">
            <button type="submit" class="btn btn-primary w-100">
                <i class="bi bi-search"></i> Tìm kiếm
            </button>
        </div>
    </form>

    <div class="phieu-scroll list-group">
        {% for phieu in phieus %}
            <div class="list-group-item">
                <a href="#" class="list-group-item list-group-item-action phieu-link text-primary" data-phieu-id="{{ phieu.id }}">
                    Phiếu số: {{ phieu.so_phieu }} | Đơn vị: {{ phieu.don_vi_nhan }} | Kho: {{ phieu.kho }} | Ngày tạo: {{ phieu.ngay_tao|date:"d/m/Y" }} | Trạng thái: {{ phieu.get_trang_thai_display }}
                </a>
                <a href="{% url 'in_phieu' phieu.id %}" target="_blank" class="btn btn-sm btn-outline-success float-end ms-2">
                    <i class="bi bi-printer"></i> In phiếu
                </a>
            </div>
        {% empty %}
            <p class="text-center">Không có phiếu nào đã được Chỉ Huy duyệt.</p>
        {% endfor %}
    </div>

    <div id="phieuDetails" class="table-responsive mt-4" style="display: none;">
        <table class="table table-bordered table-hover">
            <thead class="table-success text-center align-middle">
                <tr>
                    <th>STT</th>
                    <th>Mục đích sử dụng</th>
                    <th>Tên vật tư và quy cách</th>
                    <th>ĐVT</th>
                    <th>Số lượng yêu cầu</th>
                    <th>Số lượng ĐMV duyệt</th>
                    <th>Trạng thái</th>
                    <th>Tài khoản ĐMV duyệt</th>
                    <th>Tài khoản CH duyệt</th>
                </tr>
            </thead>
            <tbody id="phieuDetailsBody"></tbody>
        </table>
        <div class="text-end">
            <button class="btn btn-secondary mt-2" onclick="hidePhieuDetails()">Đóng</button>
        </div>
    </div>
</div>

<style>
    .phieu-scroll {
        max-height: 500px;
        overflow-y: auto;
    }
</style>

<script>
    function showPhieuDetails(phieuId) {
        fetch(`/api/phieu_ch_da_duyet/${phieuId}/`)
            .then(response => response.json())
            .then(data => {
                const tbody = document.getElementById('phieuDetailsBody');
                tbody.innerHTML = '';
                data.vat_tus.forEach((vattu, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${vattu.muc_dich_su_dung || ''}</td>
                        <td>${vattu.ten_vat_tu || ''}</td>
                        <td>${vattu.don_vi_tinh || ''}</td>
                        <td>${vattu.so_luong_yeu_cau || ''}</td>
                        <td>${vattu.so_luong_duyet || ''}</td>
                        <td>${data.phieu.trang_thai_display || ''}</td>
                        <td>${data.phieu.nguoi_duyet_dmv || ''}</td>
                        <td>${data.phieu.nguoi_duyet_ch || ''}</td>
                    `;
                    tbody.appendChild(row);
                });
                document.getElementById('phieuDetails').style.display = 'block';
            })
            .catch(error => console.error('Lỗi:', error));
    }

    function hidePhieuDetails() {
        document.getElementById('phieuDetails').style.display = 'none';
    }

    document.querySelectorAll('.phieu-link').forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const phieuId = this.getAttribute('data-phieu-id');
            showPhieuDetails(phieuId);
        });
    });
</script>
{% endblock %}