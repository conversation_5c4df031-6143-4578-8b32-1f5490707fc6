"""
Views cho PHÒNG TỔ CHỨC LAO ĐỘNG (B2)
<PERSON><PERSON><PERSON> các views cho:
- <PERSON><PERSON><PERSON> cáo quân số các đơn vị
- Qu<PERSON>n lý nhân sự
- B<PERSON>o cáo lao động
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, Avg
from django.utils import timezone
from datetime import datetime, date, timedelta
import json

# Import models từ b2_models
from z115_app.models.b2_models import *
from django.contrib.auth.models import User

@login_required
def home_b2(request):
    """Trang chủ PHÒNG TỔ CHỨC LAO ĐỘNG"""
    # Thống kê tổng quan
    tong_nhan_vien = NhanVien.objects.filter(trang_thai='dang_lam').count()
    tong_don_vi = DonVi.objects.filter(trang_thai=True).count()
    
    context = {
        'title': 'PHÒNG TỔ CHỨC LAO ĐỘNG',
        'user': request.user,
        'current_date': timezone.now().date(),
        'tong_nhan_vien': tong_nhan_vien,
        'tong_don_vi': tong_don_vi,
    }
    return render(request, 'z115_app/home_b2.html', context)

# ===== BÁO CÁO QUÂN SỐ CÁC ĐƠN VỊ =====
@login_required
def bao_cao_quan_so(request):
    """Danh sách báo cáo quân số"""
    # Lấy tham số lọc
    thang = request.GET.get('thang')
    nam = request.GET.get('nam')
    don_vi_id = request.GET.get('don_vi')
    
    # Query báo cáo
    bao_cao_list = BaoCaoQuanSo.objects.all().order_by('-nam', '-thang')
    
    if thang:
        bao_cao_list = bao_cao_list.filter(thang=thang)
    if nam:
        bao_cao_list = bao_cao_list.filter(nam=nam)
    if don_vi_id:
        bao_cao_list = bao_cao_list.filter(don_vi_id=don_vi_id)
    
    # Phân trang
    paginator = Paginator(bao_cao_list, 20)
    page_number = request.GET.get('page')
    bao_cao_page = paginator.get_page(page_number)
    
    # Danh sách đơn vị cho filter
    don_vi_list = DonVi.objects.filter(trang_thai=True).order_by('ten_don_vi')
    
    context = {
        'title': 'BÁO CÁO QUÂN SỐ CÁC ĐƠN VỊ',
        'bao_cao_page': bao_cao_page,
        'don_vi_list': don_vi_list,
        'current_thang': thang,
        'current_nam': nam,
        'current_don_vi': don_vi_id,
    }
    return render(request, 'z115_app/bao_cao_quan_so.html', context)

@login_required
def tao_bao_cao_quan_so(request):
    """Tạo báo cáo quân số mới"""
    if request.method == 'POST':
        try:
            thang = int(request.POST.get('thang'))
            nam = int(request.POST.get('nam'))
            don_vi_id = request.POST.get('don_vi')
            
            don_vi = get_object_or_404(DonVi, id=don_vi_id)
            
            # Kiểm tra báo cáo đã tồn tại chưa
            if BaoCaoQuanSo.objects.filter(thang=thang, nam=nam, don_vi=don_vi).exists():
                messages.error(request, f'Báo cáo quân số tháng {thang}/{nam} cho {don_vi.ten_don_vi} đã tồn tại!')
                return redirect('b2:bao_cao_quan_so')
            
            # Tính toán số liệu quân số tự động
            ngay_dau_thang = date(nam, thang, 1)
            if thang == 12:
                ngay_cuoi_thang = date(nam + 1, 1, 1) - timedelta(days=1)
            else:
                ngay_cuoi_thang = date(nam, thang + 1, 1) - timedelta(days=1)
            
            # Số nhân viên đầu tháng
            so_nv_dau_thang = NhanVien.objects.filter(
                don_vi=don_vi,
                ngay_vao_lam__lt=ngay_dau_thang,
                Q(ngay_nghi_viec__isnull=True) | Q(ngay_nghi_viec__gt=ngay_dau_thang)
            ).count()
            
            # Số nhân viên mới trong tháng
            so_nv_moi = NhanVien.objects.filter(
                don_vi=don_vi,
                ngay_vao_lam__gte=ngay_dau_thang,
                ngay_vao_lam__lte=ngay_cuoi_thang
            ).count()
            
            # Số nhân viên nghỉ trong tháng
            so_nv_nghi = NhanVien.objects.filter(
                don_vi=don_vi,
                ngay_nghi_viec__gte=ngay_dau_thang,
                ngay_nghi_viec__lte=ngay_cuoi_thang
            ).count()
            
            # Số nhân viên cuối tháng
            so_nv_cuoi_thang = NhanVien.objects.filter(
                don_vi=don_vi,
                ngay_vao_lam__lte=ngay_cuoi_thang,
                Q(ngay_nghi_viec__isnull=True) | Q(ngay_nghi_viec__gt=ngay_cuoi_thang)
            ).count()
            
            # Phân loại theo giới tính
            nhan_vien_cuoi_thang = NhanVien.objects.filter(
                don_vi=don_vi,
                ngay_vao_lam__lte=ngay_cuoi_thang,
                Q(ngay_nghi_viec__isnull=True) | Q(ngay_nghi_viec__gt=ngay_cuoi_thang)
            )
            
            so_nam = nhan_vien_cuoi_thang.filter(gioi_tinh='Nam').count()
            so_nu = nhan_vien_cuoi_thang.filter(gioi_tinh='Nữ').count()
            
            # Phân loại theo độ tuổi
            so_duoi_30 = 0
            so_tu_30_den_50 = 0
            so_tren_50 = 0
            
            for nv in nhan_vien_cuoi_thang:
                tuoi = nv.tuoi
                if tuoi < 30:
                    so_duoi_30 += 1
                elif 30 <= tuoi <= 50:
                    so_tu_30_den_50 += 1
                else:
                    so_tren_50 += 1
            
            # Tạo báo cáo
            bao_cao = BaoCaoQuanSo.objects.create(
                thang=thang,
                nam=nam,
                don_vi=don_vi,
                so_nhan_vien_dau_thang=so_nv_dau_thang,
                so_nhan_vien_moi=so_nv_moi,
                so_nhan_vien_nghi=so_nv_nghi,
                so_nhan_vien_cuoi_thang=so_nv_cuoi_thang,
                so_nam=so_nam,
                so_nu=so_nu,
                so_duoi_30=so_duoi_30,
                so_tu_30_den_50=so_tu_30_den_50,
                so_tren_50=so_tren_50,
                ghi_chu=request.POST.get('ghi_chu', ''),
                nguoi_tao=request.user
            )
            
            # Tạo chi tiết theo chức vụ
            chuc_vu_list = ChucVu.objects.all()
            for chuc_vu in chuc_vu_list:
                so_luong = nhan_vien_cuoi_thang.filter(chuc_vu=chuc_vu).count()
                if so_luong > 0:
                    ChiTietQuanSo.objects.create(
                        bao_cao=bao_cao,
                        chuc_vu=chuc_vu,
                        so_luong=so_luong
                    )
            
            messages.success(request, f'Tạo báo cáo quân số tháng {thang}/{nam} thành công!')
            return redirect('b2:chi_tiet_bao_cao_quan_so', bao_cao_id=bao_cao.id)
            
        except Exception as e:
            messages.error(request, f'Lỗi khi tạo báo cáo: {str(e)}')
    
    # Danh sách đơn vị
    don_vi_list = DonVi.objects.filter(trang_thai=True).order_by('ten_don_vi')
    
    context = {
        'title': 'Tạo Báo Cáo Quân Số',
        'don_vi_list': don_vi_list,
        'current_date': timezone.now().date(),
    }
    return render(request, 'z115_app/tao_bao_cao_quan_so.html', context)

@login_required
def chi_tiet_bao_cao_quan_so(request, bao_cao_id):
    """Chi tiết báo cáo quân số"""
    bao_cao = get_object_or_404(BaoCaoQuanSo, id=bao_cao_id)
    chi_tiet_list = ChiTietQuanSo.objects.filter(bao_cao=bao_cao).order_by('chuc_vu__cap_bac')
    
    context = {
        'title': f'Chi Tiết Báo Cáo Quân Số {bao_cao.thang}/{bao_cao.nam}',
        'bao_cao': bao_cao,
        'chi_tiet_list': chi_tiet_list,
    }
    return render(request, 'z115_app/chi_tiet_bao_cao_quan_so.html', context)

@login_required
def sua_bao_cao_quan_so(request, bao_cao_id):
    """Sửa báo cáo quân số"""
    bao_cao = get_object_or_404(BaoCaoQuanSo, id=bao_cao_id)
    
    if request.method == 'POST':
        try:
            # Cập nhật thông tin báo cáo
            bao_cao.ghi_chu = request.POST.get('ghi_chu', '')
            bao_cao.save()
            
            messages.success(request, 'Cập nhật báo cáo thành công!')
            return redirect('b2:chi_tiet_bao_cao_quan_so', bao_cao_id=bao_cao.id)
            
        except Exception as e:
            messages.error(request, f'Lỗi khi cập nhật: {str(e)}')
    
    context = {
        'title': 'Sửa Báo Cáo Quân Số',
        'bao_cao': bao_cao,
    }
    return render(request, 'z115_app/sua_bao_cao_quan_so.html', context)

@login_required
def xoa_bao_cao_quan_so(request, bao_cao_id):
    """Xóa báo cáo quân số"""
    if request.method == 'POST':
        try:
            bao_cao = get_object_or_404(BaoCaoQuanSo, id=bao_cao_id)
            thang_nam = f"{bao_cao.thang}/{bao_cao.nam}"
            don_vi = bao_cao.don_vi.ten_don_vi
            bao_cao.delete()
            
            messages.success(request, f'Xóa báo cáo quân số {thang_nam} - {don_vi} thành công!')
            
        except Exception as e:
            messages.error(request, f'Lỗi khi xóa báo cáo: {str(e)}')
    
    return redirect('b2:bao_cao_quan_so')

# ===== QUẢN LÝ ĐƠN VỊ =====
@login_required
def quan_ly_don_vi(request):
    """Quản lý đơn vị"""
    don_vi_list = DonVi.objects.all().order_by('ma_don_vi')
    
    context = {
        'title': 'Quản Lý Đơn Vị',
        'don_vi_list': don_vi_list,
    }
    return render(request, 'z115_app/quan_ly_don_vi.html', context)

@login_required
def them_don_vi(request):
    """Thêm đơn vị mới"""
    if request.method == 'POST':
        try:
            DonVi.objects.create(
                ma_don_vi=request.POST.get('ma_don_vi'),
                ten_don_vi=request.POST.get('ten_don_vi'),
                don_vi_cha_id=request.POST.get('don_vi_cha') or None,
                mo_ta=request.POST.get('mo_ta', ''),
                ngay_thanh_lap=request.POST.get('ngay_thanh_lap') or None,
            )
            
            messages.success(request, 'Thêm đơn vị thành công!')
            return redirect('b2:quan_ly_don_vi')
            
        except Exception as e:
            messages.error(request, f'Lỗi khi thêm đơn vị: {str(e)}')
    
    don_vi_cha_list = DonVi.objects.filter(trang_thai=True).order_by('ten_don_vi')
    
    context = {
        'title': 'Thêm Đơn Vị',
        'don_vi_cha_list': don_vi_cha_list,
    }
    return render(request, 'z115_app/them_don_vi.html', context)

# ===== PLACEHOLDER VIEWS =====
# Các views sau sẽ được implement chi tiết trong các lần cập nhật tiếp theo

@login_required
def sua_don_vi(request, don_vi_id):
    """Sửa đơn vị - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Sửa Đơn Vị'})

@login_required
def xoa_don_vi(request, don_vi_id):
    """Xóa đơn vị - placeholder"""
    return redirect('b2:quan_ly_don_vi')

@login_required
def quan_ly_chuc_vu(request):
    """Quản lý chức vụ - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Quản Lý Chức Vụ'})

@login_required
def them_chuc_vu(request):
    """Thêm chức vụ - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Thêm Chức Vụ'})

@login_required
def sua_chuc_vu(request, chuc_vu_id):
    """Sửa chức vụ - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Sửa Chức Vụ'})

@login_required
def xoa_chuc_vu(request, chuc_vu_id):
    """Xóa chức vụ - placeholder"""
    return redirect('b2:quan_ly_chuc_vu')

@login_required
def quan_ly_nhan_vien(request):
    """Quản lý nhân viên - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Quản Lý Nhân Viên'})

@login_required
def them_nhan_vien(request):
    """Thêm nhân viên - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Thêm Nhân Viên'})

@login_required
def sua_nhan_vien(request, nhan_vien_id):
    """Sửa nhân viên - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Sửa Nhân Viên'})

@login_required
def xoa_nhan_vien(request, nhan_vien_id):
    """Xóa nhân viên - placeholder"""
    return redirect('b2:quan_ly_nhan_vien')

@login_required
def chi_tiet_nhan_vien(request, nhan_vien_id):
    """Chi tiết nhân viên - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Chi Tiết Nhân Viên'})

@login_required
def lich_su_cong_tac(request):
    """Lịch sử công tác - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Lịch Sử Công Tác'})

@login_required
def lich_su_cong_tac_nhan_vien(request, nhan_vien_id):
    """Lịch sử công tác nhân viên - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Lịch Sử Công Tác Nhân Viên'})

@login_required
def them_lich_su_cong_tac(request):
    """Thêm lịch sử công tác - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Thêm Lịch Sử Công Tác'})

@login_required
def thong_ke_nhan_su(request):
    """Thống kê nhân sự - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Thống Kê Nhân Sự'})

@login_required
def thong_ke_theo_don_vi(request):
    """Thống kê theo đơn vị - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Thống Kê Theo Đơn Vị'})

@login_required
def thong_ke_theo_chuc_vu(request):
    """Thống kê theo chức vụ - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Thống Kê Theo Chức Vụ'})

@login_required
def thong_ke_theo_do_tuoi(request):
    """Thống kê theo độ tuổi - placeholder"""
    return render(request, 'z115_app/placeholder.html', {'title': 'Thống Kê Theo Độ Tuổi'})

# ===== API ENDPOINTS =====
@login_required
def api_don_vi(request):
    """API lấy danh sách đơn vị"""
    don_vi_list = list(DonVi.objects.filter(trang_thai=True).values('id', 'ma_don_vi', 'ten_don_vi'))
    return JsonResponse({'data': don_vi_list})

@login_required
def api_chuc_vu(request):
    """API lấy danh sách chức vụ"""
    chuc_vu_list = list(ChucVu.objects.values('id', 'ma_chuc_vu', 'ten_chuc_vu', 'cap_bac'))
    return JsonResponse({'data': chuc_vu_list})

@login_required
def api_nhan_vien(request):
    """API lấy danh sách nhân viên"""
    nhan_vien_list = list(NhanVien.objects.filter(trang_thai='dang_lam').values(
        'id', 'ma_nhan_vien', 'ho_ten', 'don_vi__ten_don_vi', 'chuc_vu__ten_chuc_vu'
    ))
    return JsonResponse({'data': nhan_vien_list})

@login_required
def api_thong_ke_quan_so(request):
    """API thống kê quân số"""
    # Logic thống kê sẽ được implement sau
    return JsonResponse({'data': []})

# ===== EXPORT/IMPORT =====
@login_required
def export_bao_cao_quan_so(request):
    """Export báo cáo quân số ra Excel"""
    return HttpResponse("Export báo cáo quân số")

@login_required
def export_danh_sach_nhan_vien(request):
    """Export danh sách nhân viên ra Excel"""
    return HttpResponse("Export danh sách nhân viên")

@login_required
def import_nhan_vien(request):
    """Import nhân viên từ Excel"""
    return HttpResponse("Import nhân viên")
