#!/usr/bin/env python
"""
Script reset migration hoàn toàn
"""
import sqlite3
import os

def reset_migration_table():
    """Reset bảng django_migrations"""
    print("🔄 Reset bảng django_migrations...")
    
    conn = sqlite3.connect("db.sqlite3")
    cursor = conn.cursor()
    
    try:
        # <PERSON><PERSON>a tất cả migration z115_app khỏi database
        cursor.execute("DELETE FROM django_migrations WHERE app = 'z115_app';")
        
        # Thêm lại các migration cơ bản theo đúng thứ tự
        migrations_to_add = [
            ('z115_app', '0001_initial'),
            ('z115_app', '0002_userprofile'),
            ('z115_app', '0003_baokiemvattu'),
            ('z115_app', '0004_baokiemvattu_ket_qua_kiem_tra_and_more'),
            ('z115_app', '0005_baokiemvattu_backup_date'),
            ('z115_app', '0006_baokiembackup_remove_baokiemvattu_backup_date'),
            ('z115_app', '0007_alter_baokiembackup_options_and_more'),
            ('z115_app', '0008_alter_dummy_options'),
            ('z115_app', '0009_alter_baokiembackup_dung_vao_viec_and_more'),
            ('z115_app', '0010_tonkhob3_tonkhob3backup'),
            ('z115_app', '0011_phieu_logduyet_phieuvattu_usergroup'),
            ('z115_app', '0012_phieu_tai_khoan_tao'),
            ('z115_app', '0013_alter_phieu_trang_thai'),
            ('z115_app', '0014_danhmucvattu_nhapvattu_xuatvattu'),
            ('z115_app', '0015_danhmucthanhpham_nhapvattu_quy_cach_sanxuatthanhpham_and_more'),
        ]
        
        for app, name in migrations_to_add:
            cursor.execute("""
                INSERT INTO django_migrations (app, name, applied) 
                VALUES (?, ?, datetime('now'));
            """, (app, name))
            print(f"  ✅ Thêm {name}")
        
        conn.commit()
        conn.close()
        
        print("✅ Reset migration table thành công!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi reset migration table: {e}")
        conn.rollback()
        conn.close()
        return False

def check_final_status():
    """Kiểm tra trạng thái cuối"""
    print("\n🔍 Kiểm tra trạng thái cuối...")
    
    import subprocess
    try:
        result = subprocess.run("python manage.py showmigrations z115_app", 
                              shell=True, capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            print("✅ Migration status:")
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    print(f"   {line}")
        else:
            print(f"❌ Lỗi showmigrations: {result.stderr}")
    except Exception as e:
        print(f"❌ Lỗi check status: {e}")

def main():
    """Main function"""
    print("🔄 RESET MIGRATION HOÀN TOÀN")
    print("=" * 40)
    
    if not os.path.exists("db.sqlite3"):
        print("❌ Không tìm thấy db.sqlite3")
        return
    
    if not os.path.exists("manage.py"):
        print("❌ Không tìm thấy manage.py")
        return
    
    # Reset migration table
    if reset_migration_table():
        # Kiểm tra kết quả
        check_final_status()
        
        print("\n" + "=" * 40)
        print("✅ RESET HOÀN THÀNH!")
        print("\n📝 BƯỚC TIẾP THEO:")
        print("1. python manage.py runserver *************:8000")
        print("2. Test: http://*************:8000/thanh-pham/")
        print("3. Copy folder sang máy Admin")
    else:
        print("\n❌ RESET THẤT BẠI!")

if __name__ == "__main__":
    main()
