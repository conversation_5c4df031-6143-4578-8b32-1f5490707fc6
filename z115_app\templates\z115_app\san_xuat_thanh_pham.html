{% extends 'z115_app/base.html' %}
{% load custom_filters %}
{% block title %}SẢN XUẤT THÀNH PHẨM{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">SẢN XUẤT THÀNH PHẨM</h2>

    {% if is_quan_ly %}
    <!-- Form nhập liệu một hàng ngang -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="post" action="{% url 'them_san_xuat_thanh_pham' %}" id="sanXuatForm">
                        {% csrf_token %}
                        <div class="row align-items-end">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="ngay_san_xuat">Ngày tháng:</label>
                                    <input type="date" class="form-control" id="ngay_san_xuat" name="ngay_san_xuat"
                                        required>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="ten_thanh_pham">Tên thành phẩm:</label>
                                    <input type="text" class="form-control" id="ten_thanh_pham" name="ten_thanh_pham"
                                        placeholder="Nhập tên thành phẩm..." autocomplete="off" required>
                                    <input type="hidden" id="thanh_pham_id" name="thanh_pham">
                                    <div id="suggestions" class="list-group position-absolute"
                                        style="z-index: 1000; display: none;"></div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label for="don_vi_tinh">Đvt:</label>
                                    <input type="text" class="form-control" id="don_vi_tinh" name="don_vi_tinh"
                                        readonly>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label for="so_luong">Số lượng:</label>
                                    <input type="number" class="form-control" id="so_luong" name="so_luong" step="0.01"
                                        min="0" required>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="phan_xuong_san_xuat">Phân xưởng:</label>
                                    <input type="text" class="form-control" id="phan_xuong_san_xuat"
                                        name="phan_xuong_san_xuat">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="ghi_chu">Ghi chú:</label>
                                    <input type="text" class="form-control" id="ghi_chu" name="ghi_chu">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <button type="submit" class="btn btn-success btn-sm">
                                        <i class="fas fa-save"></i> LƯU
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <button type="button" class="btn btn-info btn-sm" onclick="clearForm()">
                                        <i class="fas fa-plus"></i> Thêm mới
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
        <table class="table table-bordered table-striped align-middle">
            <thead class="table-success sticky-top">
                <tr>
                    <th>STT</th>
                    <th>Ngày tháng</th>
                    <th>Tên thành phẩm</th>
                    <th>Đvt</th>
                    <th>Số lượng sản xuất</th>
                    <th>Phân xưởng sản xuất</th>
                    <th>Ghi chú</th>
                    {% if is_quan_ly %}
                    <th>Hành động</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in san_xuat_list %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ item.ngay_san_xuat|date:"d/m/Y" }}</td>
                    <td>{{ item.thanh_pham.ten_thanh_pham }}</td>
                    <td>{{ item.thanh_pham.don_vi_tinh }}</td>
                    <td class="text-right">{{ item.so_luong|dot_format }}</td>
                    <td>{{ item.phan_xuong_san_xuat }}</td>
                    <td>{{ item.ghi_chu }}</td>
                    {% if is_quan_ly %}
                    <td>
                        <a href="{% url 'sua_san_xuat_thanh_pham' item.id %}" class="btn btn-warning btn-sm">Sửa</a>
                        <a href="{% url 'xoa_san_xuat_thanh_pham' item.id %}" class="btn btn-danger btn-sm"
                            onclick="return confirm('Bạn chắc chắn muốn xoá?');">Xoá</a>
                    </td>
                    {% endif %}
                </tr>
                {% empty %}
                <tr>
                    <td colspan="{% if is_quan_ly %}8{% else %}7{% endif %}" class="text-center text-muted">
                        Không có dữ liệu sản xuất trong khoảng thời gian này.
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Nút Quay lại -->
        <div class="mt-3">
            <a href="{% url 'thanh_pham' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> QUAY LẠI
            </a>
        </div>
    </div>
</div>

<script>
    // Dữ liệu thành phẩm cho autocomplete
    const danhMuc = [
        {% for tp in danh_muc %}
    {
        id: "{{ tp.id }}",
            ten: "{{ tp.ten_thanh_pham|escapejs }}",
                dvt: "{{ tp.don_vi_tinh|escapejs }}",
                    phan_xuong_san_xuat: "{{ tp.phan_xuong_san_xuat|escapejs }}"
    },
    {% endfor %}
    ];

    // Autocomplete cho tên thành phẩm
    document.getElementById('ten_thanh_pham').addEventListener('input', function () {
        const input = this.value.toLowerCase();
        const suggestions = document.getElementById('suggestions');

        if (input.length < 1) {
            suggestions.style.display = 'none';
            return;
        }

        const filtered = danhMuc.filter(tp =>
            tp.ten.toLowerCase().includes(input)
        );

        if (filtered.length > 0) {
            suggestions.innerHTML = '';
            filtered.forEach(tp => {
                const item = document.createElement('a');
                item.className = 'list-group-item list-group-item-action';
                item.textContent = tp.ten;
                item.onclick = function () {
                    selectThanhPham(tp);
                };
                suggestions.appendChild(item);
            });
            suggestions.style.display = 'block';
        } else {
            suggestions.style.display = 'none';
        }
    });

    // Chọn thành phẩm từ gợi ý
    function selectThanhPham(tp) {
        document.getElementById('ten_thanh_pham').value = tp.ten;
        document.getElementById('thanh_pham_id').value = tp.id;
        document.getElementById('don_vi_tinh').value = tp.dvt;
        if (!document.getElementById('phan_xuong_san_xuat').value) {
            document.getElementById('phan_xuong_san_xuat').value = tp.phan_xuong_san_xuat;
        }
        document.getElementById('suggestions').style.display = 'none';
    }

    // Ẩn gợi ý khi click ra ngoài
    document.addEventListener('click', function (e) {
        if (!e.target.closest('#ten_thanh_pham') && !e.target.closest('#suggestions')) {
            document.getElementById('suggestions').style.display = 'none';
        }
    });

    // Clear form
    function clearForm() {
        document.getElementById('sanXuatForm').reset();
        document.getElementById('thanh_pham_id').value = '';
        document.getElementById('suggestions').style.display = 'none';
    }
</script>
{% endblock %}