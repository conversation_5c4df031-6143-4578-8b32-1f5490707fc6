{% extends 'z115_app/base.html' %}
{% block title %}S<PERSON><PERSON> tồn kho KZ24 A6{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">Sửa tồn kho KZ24 A6</h2>
    <form method="post" class="row g-3 mb-4">
        {% csrf_token %}
        <div class="col-md-2">
            <input type="date" name="ngay" class="form-control" value="{{ ton.ngay|date:'Y-m-d' }}" required>
        </div>
        <div class="col-md-4">
            <input list="vat_tu_list" name="ten_vat_tu" class="form-control" value="{{ ton.vat_tu.ten_vat_tu }}" required>
            <datalist id="vat_tu_list">
                {% for vt in danh_muc %}
                <option value="{{ vt.ten_vat_tu }}">
                {% endfor %}
            </datalist>
        </div>
        <div class="col-md-2">
            <input type="text" name="so_luong" class="form-control" value="{{ ton.so_luong }}" required>
        </div>
        <div class="col-md-2">
            <input type="text" name="ghi_chu" class="form-control" value="{{ ton.ghi_chu }}">
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-success">Lưu</button>
            <a href="{% url 'ton_kho_kz24' %}" class="btn btn-secondary">Huỷ</a>
        </div>
    </form>
</div>
{% endblock %}