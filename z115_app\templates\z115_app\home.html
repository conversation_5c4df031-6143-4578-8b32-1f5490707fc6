{% extends 'z115_app/base.html' %}
{% load static %}

{% block title %}Trang chủ{% endblock %}

{% block content %}
<!-- Link offline toàn phần -->
<link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
<link href="{% static 'css/all.min.css' %}" rel="stylesheet">

<style>
    .list-group-item:hover {
        background-color: #e9ecef;
        transform: scale(1.03);
        transition: all 0.2s ease;
    }

    .custom-dropdown {
        display: none;
        background-color: #fff;
        border: 1px solid #ccc;
        padding: 5px 10px;
        border-radius: 5px;
        margin-top: 5px;
    }

    .custom-dropdown.show {
        display: block;
    }

    .custom-dropdown label {
        font-weight: bold;
        color: #0066cc;
        padding: 6px 10px;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .custom-dropdown label:hover {
        background-color: #f8f9fa;
    }

    .custom-dropdown input[type="radio"] {
        margin-right: 10px;
        accent-color: #007bff;
        transform: scale(1.2);
    }

    .dropdown-toggle::after {
        content: none;
        /* Xóa icon mặc định nếu có */
    }
</style>

<div class="d-flex m-0">
    <!-- Sidebar -->
    <div
        style="width: 280px; min-height: 80vh; border-right: 1px solid #ccc; background-color: #e6e6fa; padding: 16px 12px 16px 0;">
        <h3 class="text-primary fw-bold" style="font-size: 18pt; padding-left: 12px;">
            MENU
        </h3>
        <div class="list-group" style="padding-left: 12px;">
            {% if perms.z115_app.view_baokiem %}
            <a href="{% url 'bao_kiem_vat_tu' %}" class="list-group-item list-group-item-action fw-bold text-primary"
                style="font-size: 14pt;">
                <i class="fas fa-clipboard-check"></i> BÁO KIỂM VẬT TƯ PVSX
            </a>
            {% endif %}
            {% if perms.z115_app.view_kehoach %}
            <a href="{% url 'bao_cao_ton_kho_b3' %}" class="list-group-item list-group-item-action fw-bold text-primary"
                style="font-size: 14pt;">
                <i class="fas fa-calendar-alt"></i> BÁO CÁO TỒN KHO B3
            </a>
            {% endif %}
            {% if perms.z115_app.view_thuocno %}
            <div class="list-group-item list-group-item-action fw-bold text-primary"
                style="font-size: 14pt; position: relative; cursor: pointer;" onclick="toggleDropdown()">
                <span class="dropdown-toggle d-flex align-items-center justify-content-between">
                    <span><i class="fas fa-bomb"></i> NXT KHO XÍ NGHIỆP I</span>
                    <i class="fas fa-chevron-down ms-auto" id="dropdown-icon"></i>
                </span>
                <div class="custom-dropdown" id="customDropdown">
                    <label onclick="location.href='{% url 'vat_tu_pvsx' %}'">
                        <input type="radio" name="nxt-option"> VẬT TƯ PVSX
                    </label>
                    <label onclick="location.href='{% url 'thanh_pham' %}'">
                        <input type="radio" name="nxt-option"> THÀNH PHẨM
                    </label>
                </div>
            </div>
            {% endif %}
            {% if perms.z115_app.view_cap_vat_tu_khu_a %}
            <a href="{% url 'cap_vat_tu_khu_a' %}" class="list-group-item list-group-item-action fw-bold text-primary"
                style="font-size: 14pt;">
                <i class="fas fa-truck-loading"></i> CẤP VẬT TƯ PVSX KHU A
            </a>
            {% endif %}
            {% if perms.z115_app.view_nhap_kho_thanh_pham_khu_a %}
            <a href="{% url 'nhap_kho_thanh_pham_khu_a' %}"
                class="list-group-item list-group-item-action fw-bold text-primary" style="font-size: 14pt;">
                <i class="fas fa-warehouse"></i> NHẬP KHO THÀNH PHẨM, BTP KHU A
            </a>
            {% endif %}
            {% if user.username == 'hungpc892' and user.is_superuser %}
            <a href="{% url 'quan_ly_nguoi_dung' %}" class="list-group-item list-group-item-action fw-bold text-primary"
                style="font-size: 14pt;">
                <i class="fas fa-users-cog"></i> QUẢN LÝ NGƯỜI DÙNG
            </a>
            {% endif %}

            <!-- Separator -->
            <div class="list-group-item" style="background-color: #d1ecf1; border: none; padding: 5px;">
                <small class="text-muted fw-bold">CÁC PHÒNG BAN</small>
            </div>

            <!-- PHÒNG VẬT TƯ (B3) -->
            <a href="{% url 'b3:home_b3' %}" class="list-group-item list-group-item-action fw-bold text-success"
                style="font-size: 14pt;">
                <i class="fas fa-warehouse"></i> PHÒNG VẬT TƯ
            </a>

            <!-- PHÒNG TỔ CHỨC LAO ĐỘNG (B2) -->
            <a href="{% url 'b2:home_b2' %}" class="list-group-item list-group-item-action fw-bold text-info"
                style="font-size: 14pt;">
                <i class="fas fa-users"></i> PHÒNG TỔ CHỨC LAO ĐỘNG
            </a>
        </div>
    </div>

    <!-- Nội dung chính -->
    <div class="flex-fill text-center p-3" style="background-color: #90ee90;">
        <h1 class="text-primary fw-bold" style="font-size: 24pt;">
            WELCOME WEBSITE PHÒNG VẬT TƯ
        </h1>
        <h2 class="text-success" style="font-size: 18pt;">
            Xin chào, {{ username }}!
        </h2>
        <img src="{% static 'images/trang_chu.jpg' %}" alt="Hình nền đăng nhập"
            style="max-width: 80%; height: 70vh; object-fit: cover; margin: 20px auto;">
    </div>
</div>

<script>
    function toggleDropdown() {
        const dropdown = document.getElementById('customDropdown');
        const icon = document.getElementById('dropdown-icon');
        dropdown.classList.toggle('show');
        icon.classList.toggle('fa-chevron-down');
        icon.classList.toggle('fa-chevron-up');
    }
</script>
{% endblock %}