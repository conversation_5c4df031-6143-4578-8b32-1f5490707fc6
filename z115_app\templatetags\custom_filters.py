from django import template

register = template.Library()

@register.filter
def contains(value, arg):
    return arg in value

@register.filter
def get_item(dictionary, key):
    return dictionary.get(key, [])

@register.filter
def dot_format(value):
    """Format number with dot as thousand separator"""
    try:
        # Convert to float first, then to int to remove decimals
        num = int(float(value))
        # Format with dot separator
        return f"{num:,}".replace(',', '.')
    except (ValueError, TypeError):
        return value

@register.filter
def dot_format_hide_zero(value):
    """Format number with dot separator, hide if zero"""
    try:
        # Convert to float first, then to int to remove decimals
        num = int(float(value))
        # Return empty string if zero
        if num == 0:
            return ""
        # Format with dot separator
        return f"{num:,}".replace(',', '.')
    except (ValueError, TypeError):
        return value

@register.filter
def add_dots(value):
    """Add dots as thousand separator - alias for dot_format"""
    try:
        # Convert to float first, then to int to remove decimals
        num = int(float(value))
        # Format with dot separator
        return f"{num:,}".replace(',', '.')
    except (ValueError, TypeError):
        return value