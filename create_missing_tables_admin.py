#!/usr/bin/env python
"""
Script tạo các bảng thiếu cho máy <PERSON>
"""
import sqlite3
import os

def check_existing_tables():
    """Kiểm tra bảng hiện có"""
    print("🔍 KIỂM TRA BẢNG HIỆN CÓ")
    print("=" * 30)
    
    conn = sqlite3.connect("db.sqlite3")
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'z115_app_%' ORDER BY name;")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 Có {len(tables)} bảng z115_app:")
        for table in tables:
            print(f"  ✅ {table}")
        
        conn.close()
        return tables
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        conn.close()
        return []

def create_missing_tables():
    """Tạo các bảng thiếu"""
    print("\n🔧 TẠO CÁC BẢNG THIẾU")
    print("=" * 30)
    
    conn = sqlite3.connect("db.sqlite3")
    cursor = conn.cursor()
    
    try:
        # Tạo bảng SanXuatThanhPham
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "z115_app_sanxuatthanhpham" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "ngay_san_xuat" date NOT NULL,
                "so_luong" real NOT NULL,
                "phan_xuong_san_xuat" varchar(255) NOT NULL,
                "ghi_chu" varchar(255) NOT NULL,
                "thanh_pham_id" bigint NOT NULL REFERENCES "z115_app_danhmucthanhpham" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        print("✅ Tạo z115_app_sanxuatthanhpham")

        # Tạo bảng TieuThuThanhPham
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "z115_app_tieuthuthanhpham" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "ngay_tieu_thu" date NOT NULL,
                "so_luong" real NOT NULL,
                "ho_khach_hang" varchar(255) NOT NULL,
                "ghi_chu" varchar(255) NOT NULL,
                "thanh_pham_id" bigint NOT NULL REFERENCES "z115_app_danhmucthanhpham" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        print("✅ Tạo z115_app_tieuthuthanhpham")

        # Tạo bảng SanXuatTNCN
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "z115_app_sanxuattncn" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "ngay_san_xuat" date NOT NULL,
                "so_luong" real NOT NULL,
                "phan_xuong_san_xuat" varchar(255) NOT NULL,
                "ghi_chu" varchar(255) NOT NULL,
                "thanh_pham_id" bigint NOT NULL REFERENCES "z115_app_danhmucthanhpham" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        print("✅ Tạo z115_app_sanxuattncn")

        # Tạo bảng TieuThuTNCN
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "z115_app_tieuthutncn" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "ngay_tieu_thu" date NOT NULL,
                "so_luong" real NOT NULL,
                "ho_khach_hang" varchar(255) NOT NULL,
                "ghi_chu" varchar(255) NOT NULL,
                "thanh_pham_id" bigint NOT NULL REFERENCES "z115_app_danhmucthanhpham" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        print("✅ Tạo z115_app_tieuthutncn")

        # Tạo bảng TonKhoKZ24
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "z115_app_tonkhokz24" (
                "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
                "ngay" date NOT NULL,
                "so_luong" real NOT NULL,
                "ghi_chu" varchar(255) NOT NULL,
                "vat_tu_id" bigint NOT NULL REFERENCES "z115_app_danhmucvattu" ("id") DEFERRABLE INITIALLY DEFERRED
            );
        """)
        print("✅ Tạo z115_app_tonkhokz24")

        # Thêm cột quy_cach vào nhapvattu nếu chưa có
        try:
            cursor.execute('ALTER TABLE "z115_app_nhapvattu" ADD COLUMN "quy_cach" varchar(255) DEFAULT "";')
            print("✅ Thêm cột quy_cach vào z115_app_nhapvattu")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("✅ Cột quy_cach đã tồn tại")
            else:
                print(f"⚠️ Lỗi thêm cột quy_cach: {e}")

        # Thêm cột phan_xuong_san_xuat vào danhmucthanhpham nếu chưa có
        try:
            cursor.execute('ALTER TABLE "z115_app_danhmucthanhpham" ADD COLUMN "phan_xuong_san_xuat" varchar(255) DEFAULT "";')
            print("✅ Thêm cột phan_xuong_san_xuat vào z115_app_danhmucthanhpham")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("✅ Cột phan_xuong_san_xuat đã tồn tại")
            else:
                print(f"⚠️ Lỗi thêm cột phan_xuong_san_xuat: {e}")

        # Commit changes
        conn.commit()
        conn.close()
        
        print("\n✅ TẠO BẢNG THÀNH CÔNG!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi tạo bảng: {e}")
        conn.rollback()
        conn.close()
        return False

def verify_tables():
    """Kiểm tra lại bảng sau khi tạo"""
    print("\n🔍 KIỂM TRA LẠI SAU KHI TẠO")
    print("=" * 30)
    
    conn = sqlite3.connect("db.sqlite3")
    cursor = conn.cursor()
    
    required_tables = [
        'z115_app_danhmucthanhpham',
        'z115_app_sanxuatthanhpham',
        'z115_app_tieuthuthanhpham', 
        'z115_app_sanxuattncn',
        'z115_app_tieuthutncn',
        'z115_app_tonkhokz24'
    ]
    
    try:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'z115_app_%' ORDER BY name;")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table in existing_tables:
                print(f"  ✅ {table}")
            else:
                missing_tables.append(table)
                print(f"  ❌ {table} - VẪN THIẾU")
        
        conn.close()
        
        if len(missing_tables) == 0:
            print("\n✅ TẤT CẢ BẢNG ĐÃ CÓ!")
            return True
        else:
            print(f"\n❌ VẪN THIẾU {len(missing_tables)} BẢNG!")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi kiểm tra: {e}")
        conn.close()
        return False

def main():
    """Main function"""
    print("🔧 TẠO BẢNG THIẾU CHO MÁY ADMIN")
    print("=" * 50)
    
    if not os.path.exists("db.sqlite3"):
        print("❌ Không tìm thấy db.sqlite3")
        return
    
    if not os.path.exists("manage.py"):
        print("❌ Không tìm thấy manage.py")
        return
    
    print(f"📁 Thư mục: {os.getcwd()}")
    
    # Kiểm tra bảng hiện có
    existing_tables = check_existing_tables()
    
    # Tạo bảng thiếu
    if create_missing_tables():
        # Kiểm tra lại
        if verify_tables():
            print("\n" + "=" * 50)
            print("✅ HOÀN THÀNH TẠO BẢNG!")
            print("\n🚀 CHẠY SERVER:")
            print("python manage.py runserver 192.170.5.186:8000")
            print("\n🌐 TEST:")
            print("http://192.170.5.186:8000/thanh-pham/")
            print("\n📝 Nếu vẫn lỗi, restart server và thử lại")
        else:
            print("\n❌ VẪN CÓ BẢNG THIẾU!")
    else:
        print("\n❌ TẠO BẢNG THẤT BẠI!")

if __name__ == "__main__":
    main()
