"""
Views cho PHÒNG VẬT TƯ (B3)
<PERSON><PERSON> <PERSON><PERSON><PERSON> các views cho:
- <PERSON><PERSON><PERSON> kiểm vật tư PVSX
- B<PERSON><PERSON> cáo tồn kho B3
- XNT kho xí nghiệp I
- Cấp vật tư khu A PVSX
- Nhập kho thành phẩm, BTP khu A
- Quản lý người dùng
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import datetime, date, timedelta
import json

# Import models từ b3_models
from z115_app.models.b3_models import *
from z115_app.models import Dummy

# Import các views hiện tại để tái sử dụng
from z115_app.views.bao_kiem_views import *
from z115_app.views.nxt_views import *

@login_required
def home_b3(request):
    """Trang chủ PHÒNG VẬT TƯ"""
    context = {
        'title': 'PHÒNG VẬT TƯ',
        'user': request.user,
        'current_date': timezone.now().date(),
    }
    return render(request, 'z115_app/home_b3.html', context)

# ===== BÁO KIỂM VẬT TƯ PVSX =====
# Sử dụng lại các views từ bao_kiem_views.py
# Các views này đã được import ở trên

# ===== BÁO CÁO TỒN KHO B3 =====
# Sử dụng lại các views từ nxt_views.py
# Các views này đã được import ở trên

# ===== XNT KHO XÍ NGHIỆP I =====
@login_required
@permission_required('z115_app.view_baokiem', raise_exception=True)
def xnt_kho_xi_nghiep(request):
    """View cho XNT kho xí nghiệp I"""
    context = {
        'title': 'XNT KHO XÍ NGHIỆP I',
        'user': request.user,
    }
    return render(request, 'z115_app/xnt_kho_xi_nghiep.html', context)

# ===== CẤP VẬT TƯ KHU A PVSX =====
@login_required
@permission_required('z115_app.view_cap_vat_tu_khu_a', raise_exception=True)
def cap_vat_tu_khu_a(request):
    """View chính cho cấp vật tư khu A PVSX"""
    context = {
        'title': 'CẤP VẬT TƯ KHU A PVSX',
        'user': request.user,
    }
    return render(request, 'z115_app/cap_vat_tu_khu_a.html', context)

@login_required
def tao_phieu_vat_tu(request):
    """Tạo phiếu vật tư mới"""
    if request.method == 'POST':
        try:
            # Tạo số phiếu tự động
            today = date.today()
            so_phieu_prefix = f"VT{today.strftime('%Y%m%d')}"
            last_phieu = Phieu.objects.filter(
                so_phieu__startswith=so_phieu_prefix
            ).order_by('-so_phieu').first()
            
            if last_phieu:
                last_number = int(last_phieu.so_phieu[-3:])
                new_number = last_number + 1
            else:
                new_number = 1
            
            so_phieu = f"{so_phieu_prefix}{new_number:03d}"
            
            # Tạo phiếu mới
            phieu = Phieu.objects.create(
                so_phieu=so_phieu,
                tai_khoan_tao=request.user,
                ghi_chu=request.POST.get('ghi_chu', '')
            )
            
            # Thêm các vật tư vào phiếu
            vat_tu_data = json.loads(request.POST.get('vat_tu_data', '[]'))
            for item in vat_tu_data:
                PhieuVatTu.objects.create(
                    phieu=phieu,
                    ten_vat_tu=item['ten_vat_tu'],
                    don_vi_tinh=item['don_vi_tinh'],
                    so_luong=float(item['so_luong']),
                    ghi_chu=item.get('ghi_chu', '')
                )
            
            messages.success(request, f'Tạo phiếu {so_phieu} thành công!')
            return redirect('b3:lich_su_phieu_vat_tu')
            
        except Exception as e:
            messages.error(request, f'Lỗi khi tạo phiếu: {str(e)}')
    
    # Lấy danh sách vật tư
    danh_muc_vat_tu = DanhMucVatTu.objects.all().order_by('ten_vat_tu')
    
    context = {
        'title': 'Tạo Phiếu Vật Tư',
        'danh_muc_vat_tu': danh_muc_vat_tu,
    }
    return render(request, 'z115_app/tao_phieu_vat_tu.html', context)

@login_required
def duyet_phieu_vat_tu(request):
    """Duyệt phiếu vật tư"""
    # Chỉ DMV và CH mới được duyệt phiếu
    if request.user.username not in ['hungpc892', 'HUYNHBNV']:
        messages.error(request, 'Bạn không có quyền duyệt phiếu!')
        return redirect('b3:home_b3')
    
    if request.method == 'POST':
        phieu_id = request.POST.get('phieu_id')
        hanh_dong = request.POST.get('hanh_dong')  # 'duyet_dmv', 'duyet_ch', 'huy'
        ghi_chu = request.POST.get('ghi_chu', '')
        
        try:
            phieu = get_object_or_404(Phieu, id=phieu_id)
            
            # Cập nhật trạng thái phiếu
            if hanh_dong == 'duyet_dmv':
                phieu.trang_thai = 'dmv_duyet'
            elif hanh_dong == 'duyet_ch':
                phieu.trang_thai = 'ch_duyet'
            elif hanh_dong == 'huy':
                phieu.trang_thai = 'huy'
            
            phieu.save()
            
            # Ghi log duyệt
            LogDuyet.objects.create(
                phieu=phieu,
                nguoi_duyet=request.user,
                hanh_dong=hanh_dong,
                ghi_chu=ghi_chu
            )
            
            messages.success(request, f'Đã {hanh_dong} phiếu {phieu.so_phieu}!')
            
        except Exception as e:
            messages.error(request, f'Lỗi khi duyệt phiếu: {str(e)}')
    
    # Lấy danh sách phiếu chờ duyệt
    phieu_cho_duyet = Phieu.objects.filter(
        trang_thai__in=['cho_duyet', 'dmv_duyet']
    ).order_by('-ngay_tao')
    
    context = {
        'title': 'Duyệt Phiếu Vật Tư',
        'phieu_cho_duyet': phieu_cho_duyet,
    }
    return render(request, 'z115_app/duyet_phieu_vat_tu.html', context)

@login_required
def lich_su_phieu_vat_tu(request):
    """Lịch sử phiếu vật tư"""
    # Lấy danh sách phiếu
    phieu_list = Phieu.objects.all().order_by('-ngay_tao')
    
    # Phân trang
    paginator = Paginator(phieu_list, 20)
    page_number = request.GET.get('page')
    phieu_page = paginator.get_page(page_number)
    
    context = {
        'title': 'Lịch Sử Phiếu Vật Tư',
        'phieu_page': phieu_page,
    }
    return render(request, 'z115_app/lich_su_phieu_vat_tu.html', context)

# ===== NHẬP KHO THÀNH PHẨM, BTP KHU A =====
@login_required
@permission_required('z115_app.view_nhap_kho_thanh_pham_khu_a', raise_exception=True)
def nhap_kho_thanh_pham(request):
    """View cho nhập kho thành phẩm"""
    context = {
        'title': 'NHẬP KHO THÀNH PHẨM',
        'user': request.user,
    }
    return render(request, 'z115_app/nhap_kho_thanh_pham.html', context)

@login_required
@permission_required('z115_app.view_nhap_kho_thanh_pham_khu_a', raise_exception=True)
def btp_khu_a(request):
    """View cho BTP khu A"""
    context = {
        'title': 'BTP KHU A',
        'user': request.user,
    }
    return render(request, 'z115_app/btp_khu_a.html', context)

# ===== QUẢN LÝ NGƯỜI DÙNG =====
@login_required
def quan_ly_nguoi_dung(request):
    """Quản lý người dùng - chỉ admin mới được truy cập"""
    if not request.user.is_superuser:
        messages.error(request, 'Bạn không có quyền truy cập!')
        return redirect('b3:home_b3')
    
    from django.contrib.auth.models import User
    users = User.objects.all().order_by('username')
    
    context = {
        'title': 'Quản Lý Người Dùng',
        'users': users,
    }
    return render(request, 'z115_app/quan_ly_nguoi_dung.html', context)

@login_required
def them_nguoi_dung(request):
    """Thêm người dùng mới"""
    if not request.user.is_superuser:
        messages.error(request, 'Bạn không có quyền truy cập!')
        return redirect('b3:home_b3')
    
    if request.method == 'POST':
        try:
            from django.contrib.auth.models import User
            username = request.POST.get('username')
            password = request.POST.get('password')
            email = request.POST.get('email', '')
            first_name = request.POST.get('first_name', '')
            last_name = request.POST.get('last_name', '')
            
            # Tạo user mới
            user = User.objects.create_user(
                username=username,
                password=password,
                email=email,
                first_name=first_name,
                last_name=last_name
            )
            
            # Tạo UserProfile
            UserProfile.objects.create(user=user)
            
            messages.success(request, f'Tạo người dùng {username} thành công!')
            return redirect('b3:quan_ly_nguoi_dung')
            
        except Exception as e:
            messages.error(request, f'Lỗi khi tạo người dùng: {str(e)}')
    
    context = {
        'title': 'Thêm Người Dùng',
    }
    return render(request, 'z115_app/them_nguoi_dung.html', context)

@login_required
def sua_nguoi_dung(request, user_id):
    """Sửa thông tin người dùng"""
    if not request.user.is_superuser:
        messages.error(request, 'Bạn không có quyền truy cập!')
        return redirect('b3:home_b3')
    
    from django.contrib.auth.models import User
    user = get_object_or_404(User, id=user_id)
    
    if request.method == 'POST':
        try:
            user.email = request.POST.get('email', '')
            user.first_name = request.POST.get('first_name', '')
            user.last_name = request.POST.get('last_name', '')
            user.is_active = request.POST.get('is_active') == 'on'
            user.save()
            
            messages.success(request, f'Cập nhật người dùng {user.username} thành công!')
            return redirect('b3:quan_ly_nguoi_dung')
            
        except Exception as e:
            messages.error(request, f'Lỗi khi cập nhật: {str(e)}')
    
    context = {
        'title': 'Sửa Người Dùng',
        'user_obj': user,
    }
    return render(request, 'z115_app/sua_nguoi_dung.html', context)

@login_required
def xoa_nguoi_dung(request, user_id):
    """Xóa người dùng"""
    if not request.user.is_superuser:
        messages.error(request, 'Bạn không có quyền truy cập!')
        return redirect('b3:home_b3')
    
    if request.method == 'POST':
        try:
            from django.contrib.auth.models import User
            user = get_object_or_404(User, id=user_id)
            username = user.username
            user.delete()
            
            messages.success(request, f'Xóa người dùng {username} thành công!')
            
        except Exception as e:
            messages.error(request, f'Lỗi khi xóa người dùng: {str(e)}')
    
    return redirect('b3:quan_ly_nguoi_dung')

@login_required
def phan_quyen_nguoi_dung(request):
    """Phân quyền người dùng"""
    if not request.user.is_superuser:
        messages.error(request, 'Bạn không có quyền truy cập!')
        return redirect('b3:home_b3')
    
    context = {
        'title': 'Phân Quyền Người Dùng',
    }
    return render(request, 'z115_app/phan_quyen_nguoi_dung.html', context)

# ===== API ENDPOINTS =====
@login_required
def api_danh_muc_vat_tu(request):
    """API lấy danh mục vật tư"""
    vat_tu_list = list(DanhMucVatTu.objects.values('id', 'ten_vat_tu', 'don_vi_tinh'))
    return JsonResponse({'data': vat_tu_list})

@login_required
def api_ton_kho_vat_tu(request):
    """API lấy tồn kho vật tư"""
    # Logic tính tồn kho sẽ được implement sau
    return JsonResponse({'data': []})

@login_required
def api_thong_ke_bao_kiem(request):
    """API thống kê báo kiểm"""
    # Logic thống kê sẽ được implement sau
    return JsonResponse({'data': []})

# ===== EXPORT/IMPORT =====
@login_required
def export_bao_kiem(request):
    """Export báo kiểm ra Excel"""
    # Logic export sẽ được implement sau
    return HttpResponse("Export báo kiểm")

@login_required
def import_bao_kiem(request):
    """Import báo kiểm từ Excel"""
    # Logic import sẽ được implement sau
    return HttpResponse("Import báo kiểm")

@login_required
def export_ton_kho(request):
    """Export tồn kho ra Excel"""
    # Logic export sẽ được implement sau
    return HttpResponse("Export tồn kho")

@login_required
def import_ton_kho(request):
    """Import tồn kho từ Excel"""
    # Logic import sẽ được implement sau
    return HttpResponse("Import tồn kho")

# ===== PLACEHOLDER VIEWS =====
# Các views này sẽ được implement chi tiết sau
def placeholder_view(request, title="Chức năng đang phát triển"):
    """View placeholder cho các chức năng chưa implement"""
    context = {
        'title': title,
        'message': 'Chức năng này đang được phát triển và sẽ sớm được cập nhật.',
    }
    return render(request, 'z115_app/placeholder.html', context)
