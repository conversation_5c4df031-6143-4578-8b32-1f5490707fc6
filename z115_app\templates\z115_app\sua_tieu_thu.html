{% extends 'z115_app/base.html' %}
{% block title %}SỬA TIÊU THỤ THÀNH PHẨM{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">SỬA TIÊU THỤ THÀNH PHẨM</h2>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning">
                    <h4 class="card-title mb-0"><i class="fas fa-edit"></i> Chỉnh sửa thông tin tiêu thụ</h4>
                </div>
                <div class="card-body">
                    <form method="post" id="editForm">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="ngay_tieu_thu">Ngày tiêu thụ:</label>
                                    <input type="date" class="form-control" id="ngay_tieu_thu" name="ngay_tieu_thu" 
                                           value="{{ tieu_thu.ngay_tieu_thu|date:'Y-m-d' }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="ten_thanh_pham">Tên thành phẩm:</label>
                                    <input type="text" class="form-control" id="ten_thanh_pham" name="ten_thanh_pham" 
                                           value="{{ tieu_thu.thanh_pham.ten_thanh_pham }}" 
                                           placeholder="Nhập tên thành phẩm..." autocomplete="off" required>
                                    <input type="hidden" id="thanh_pham_id" name="thanh_pham" value="{{ tieu_thu.thanh_pham.id }}">
                                    <div id="suggestions" class="list-group position-absolute" style="z-index: 1000; display: none;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="don_vi_tinh">Đơn vị tính:</label>
                                    <input type="text" class="form-control" id="don_vi_tinh" name="don_vi_tinh" 
                                           value="{{ tieu_thu.thanh_pham.don_vi_tinh }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="so_luong">Số lượng:</label>
                                    <input type="number" class="form-control" id="so_luong" name="so_luong" 
                                           value="{{ tieu_thu.so_luong }}" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="ho_khach_hang">Hộ khách hàng:</label>
                                    <input type="text" class="form-control" id="ho_khach_hang" name="ho_khach_hang" 
                                           value="{{ tieu_thu.ho_khach_hang }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="noi_tieu_thu">Nơi tiêu thụ:</label>
                                    <input type="text" class="form-control" id="noi_tieu_thu" name="noi_tieu_thu" 
                                           value="{{ tieu_thu.noi_tieu_thu }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="ghi_chu">Ghi chú:</label>
                                    <input type="text" class="form-control" id="ghi_chu" name="ghi_chu" 
                                           value="{{ tieu_thu.ghi_chu }}">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <button type="submit" class="btn btn-success mr-2">
                                    <i class="fas fa-save"></i> CẬP NHẬT
                                </button>
                                <a href="{% url 'tieu_thu' %}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> HỦY
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Dữ liệu thành phẩm cho autocomplete
    const danhMuc = [
        {% for tp in danh_muc %}
        {
            id: "{{ tp.id }}",
            ten: "{{ tp.ten_thanh_pham|escapejs }}",
            dvt: "{{ tp.don_vi_tinh|escapejs }}",
            ho_khach_hang: "{{ tp.ho_khach_hang|escapejs }}",
            noi_tieu_thu: "{{ tp.noi_tieu_thu|escapejs }}"
        },
        {% endfor %}
    ];

    // Autocomplete cho tên thành phẩm
    document.getElementById('ten_thanh_pham').addEventListener('input', function() {
        const input = this.value.toLowerCase();
        const suggestions = document.getElementById('suggestions');
        
        if (input.length < 1) {
            suggestions.style.display = 'none';
            return;
        }
        
        const filtered = danhMuc.filter(tp => 
            tp.ten.toLowerCase().includes(input)
        );
        
        if (filtered.length > 0) {
            suggestions.innerHTML = '';
            filtered.forEach(tp => {
                const item = document.createElement('a');
                item.className = 'list-group-item list-group-item-action';
                item.textContent = tp.ten;
                item.onclick = function() {
                    selectThanhPham(tp);
                };
                suggestions.appendChild(item);
            });
            suggestions.style.display = 'block';
        } else {
            suggestions.style.display = 'none';
        }
    });

    // Chọn thành phẩm từ gợi ý
    function selectThanhPham(tp) {
        document.getElementById('ten_thanh_pham').value = tp.ten;
        document.getElementById('thanh_pham_id').value = tp.id;
        document.getElementById('don_vi_tinh').value = tp.dvt;
        if (!document.getElementById('ho_khach_hang').value) {
            document.getElementById('ho_khach_hang').value = tp.ho_khach_hang;
        }
        if (!document.getElementById('noi_tieu_thu').value) {
            document.getElementById('noi_tieu_thu').value = tp.noi_tieu_thu;
        }
        document.getElementById('suggestions').style.display = 'none';
    }

    // Ẩn gợi ý khi click ra ngoài
    document.addEventListener('click', function(e) {
        if (!e.target.closest('#ten_thanh_pham') && !e.target.closest('#suggestions')) {
            document.getElementById('suggestions').style.display = 'none';
        }
    });
</script>
{% endblock %}
