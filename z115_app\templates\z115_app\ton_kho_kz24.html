{% extends 'z115_app/base.html' %}
{% load custom_filters %}
{% block title %}TỒN KHO KZ24 A6{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">TỒN KHO KZ24 A6</h2>
    <form method="post" class="row g-3 mb-4">
        {% csrf_token %}
        <div class="col-md-2">
            <input type="date" name="ngay" class="form-control" required>
        </div>
        <div class="col-md-4">
            <input list="vat_tu_list" name="ten_vat_tu" class="form-control" placeholder="Tên vật tư" required>
            <datalist id="vat_tu_list">
                {% for vt in danh_muc %}
                <option value="{{ vt.ten_vat_tu }}">
                    {% endfor %}
            </datalist>
        </div>
        <div class="col-md-2">
            <input type="text" name="so_luong" class="form-control" placeholder="Số lượng tồn kho" required>
        </div>
        <div class="col-md-2">
            <input type="text" name="ghi_chu" class="form-control" placeholder="Ghi chú">
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-success">Lưu</button>
        </div>
    </form>
    <h5 class="mb-3">Danh sách tồn kho KZ24 A6</h5>
    <table class="table table-bordered table-striped table-sm">
        <thead class="table-info">
            <tr>
                <th>STT</th>
                <th>Ngày tháng</th>
                <th>Tên vật tư</th>
                <th>Đvt</th>
                <th>Số lượng tồn kho</th>
                <th>Ghi chú</th>
                <th>Hành động</th>
            </tr>
        </thead>
        <tbody>
            {% for item in ton_kho_list %}
            <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ item.ngay|date:"d/m/Y" }}</td>
                <td>{{ item.vat_tu.ten_vat_tu }}</td>
                <td>{{ item.vat_tu.don_vi_tinh }}</td>
                <td>{{ item.so_luong|dot_format }}</td>
                <td>{{ item.ghi_chu }}</td>
                <td>
                    <a href="{% url 'sua_ton_kho_kz24' item.id %}" class="btn btn-warning btn-sm">Sửa</a>
                    <a href="{% url 'xoa_ton_kho_kz24' item.id %}" class="btn btn-danger btn-sm"
                        onclick="return confirm('Bạn chắc chắn muốn xoá?');">Xoá</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Nút Quay lại -->
    <div class="mt-3">
        <a href="{% url 'vat_tu_pvsx' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> QUAY LẠI
        </a>
    </div>
</div>
{% endblock %}