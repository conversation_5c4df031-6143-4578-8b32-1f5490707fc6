"""
Models cho PHÒNG VẬT TƯ (B3)
<PERSON><PERSON> gồ<PERSON> các models liên quan đến:
- <PERSON><PERSON><PERSON> kiểm vật tư PVSX
- B<PERSON><PERSON> cáo tồn kho B3
- XNT kho xí nghiệp I
- Cấp vật tư khu A PVSX
- Nhập kho thành phẩm, BTP khu A
- Quản lý người dùng
"""

from django.db import models
from django.contrib.auth.models import User

# ===== MODELS CHO BÁO KIỂM VẬT TƯ =====
class BaoKiemVatTu(models.Model):
    ngay = models.DateField()
    ma_vlspp = models.CharField(max_length=20, blank=True)
    ma_kho = models.CharField(max_length=20)  # Bắt buộc, không để blank=True
    ten_quy_cach = models.CharField(max_length=200)
    dung_vao_viec = models.CharField(max_length=200)
    don_vi_tinh = models.CharField(max_length=50)
    so_luong = models.FloatField(default=0.0)
    nguoi_bao_kiem = models.CharField(max_length=100)
    noi_de_vat_tu = models.CharField(max_length=100)
    so_hop_cach = models.FloatField(default=0.0, null=True, blank=True)
    ket_qua_kiem_tra = models.CharField(max_length=200, blank=True)
    ngay_tra_ket_qua = models.DateField(null=True, blank=True)
    phong_b12_ky = models.CharField(max_length=100, blank=True)
    phong_b3_ky = models.CharField(max_length=100, blank=True)

    def save(self, *args, **kwargs):
        if self.ma_vlspp:
            self.ma_kho = self.ma_vlspp[:2]  # Lấy 2 ký tự đầu
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.ten_quy_cach} - {self.ngay}"

class BaoKiemBackup(models.Model):
    ngay = models.DateField()
    ma_vlspp = models.CharField(max_length=100, blank=True)
    ma_kho = models.CharField(max_length=100, blank=True)
    ten_quy_cach = models.CharField(max_length=200)
    dung_vao_viec = models.CharField(max_length=200)
    don_vi_tinh = models.CharField(max_length=50)
    so_luong = models.FloatField(default=0.0)
    nguoi_bao_kiem = models.CharField(max_length=100)
    noi_de_vat_tu = models.CharField(max_length=100)
    so_hop_cach = models.FloatField(default=0.0, null=True, blank=True)
    ket_qua_kiem_tra = models.CharField(max_length=200, blank=True)
    ngay_tra_ket_qua = models.DateField(null=True, blank=True)
    phong_b12_ky = models.CharField(max_length=100, blank=True)
    phong_b3_ky = models.CharField(max_length=100, blank=True)
    backup_date = models.DateTimeField()

    def __str__(self):
        return f"Backup {self.ten_quy_cach} - {self.backup_date}"

# ===== MODELS CHO TỒN KHO B3 =====
class TonKhoB3(models.Model):
    ngay = models.DateField()
    ten_vat_tu = models.CharField(max_length=200)
    don_vi_tinh = models.CharField(max_length=50)
    ton_dau = models.FloatField(default=0.0)
    nhap_trong_ky = models.FloatField(default=0.0)
    xuat_trong_ky = models.FloatField(default=0.0)
    ton_cuoi = models.FloatField(default=0.0)

    def __str__(self):
        return f"{self.ten_vat_tu} - {self.ngay}"

class TonKhoB3Backup(models.Model):
    backup_date = models.DateTimeField()
    ngay = models.DateField()
    ten_vat_tu = models.CharField(max_length=200)
    don_vi_tinh = models.CharField(max_length=50)
    ton_dau = models.FloatField(default=0.0)
    nhap_trong_ky = models.FloatField(default=0.0)
    xuat_trong_ky = models.FloatField(default=0.0)
    ton_cuoi = models.FloatField(default=0.0)

    def __str__(self):
        return f"Backup {self.ten_vat_tu} - {self.backup_date}"

# ===== MODELS CHO DANH MỤC VẬT TƯ =====
class DanhMucVatTu(models.Model):
    ten_vat_tu = models.CharField(max_length=200)
    don_vi_tinh = models.CharField(max_length=50)
    ma_vat_tu = models.CharField(max_length=50, blank=True)
    ghi_chu = models.TextField(blank=True)

    def __str__(self):
        return self.ten_vat_tu

class NhapVatTu(models.Model):
    vat_tu = models.ForeignKey(DanhMucVatTu, on_delete=models.CASCADE)
    ngay_nhap = models.DateField()
    so_luong = models.FloatField()
    nha_cung_cap = models.CharField(max_length=200, blank=True)
    xuat_xu = models.CharField(max_length=200, blank=True)
    quy_cach = models.CharField(max_length=200, blank=True)

    def __str__(self):
        return f"Nhập {self.vat_tu.ten_vat_tu} - {self.ngay_nhap}"

class XuatVatTu(models.Model):
    vat_tu = models.ForeignKey(DanhMucVatTu, on_delete=models.CASCADE)
    ngay_xuat = models.DateField()
    so_luong = models.FloatField()
    ghi_chu = models.CharField(max_length=200, blank=True)

    def __str__(self):
        return f"Xuất {self.vat_tu.ten_vat_tu} - {self.ngay_xuat}"

# ===== MODELS CHO THÀNH PHẨM =====
class DanhMucThanhPham(models.Model):
    ten_thanh_pham = models.CharField(max_length=200)
    don_vi_tinh = models.CharField(max_length=50)
    ho_khach_hang = models.CharField(max_length=200, blank=True)
    noi_tieu_thu = models.CharField(max_length=200, blank=True)
    phan_xuong_san_xuat = models.CharField(max_length=200, blank=True)

    def __str__(self):
        return self.ten_thanh_pham

class SanXuatThanhPham(models.Model):
    thanh_pham = models.ForeignKey(DanhMucThanhPham, on_delete=models.CASCADE)
    ngay_san_xuat = models.DateField()
    so_luong = models.FloatField()
    phan_xuong_san_xuat = models.CharField(max_length=200, blank=True)
    ghi_chu = models.TextField(blank=True)

    def __str__(self):
        return f"SX {self.thanh_pham.ten_thanh_pham} - {self.ngay_san_xuat}"

class TieuThuThanhPham(models.Model):
    thanh_pham = models.ForeignKey(DanhMucThanhPham, on_delete=models.CASCADE)
    ngay_tieu_thu = models.DateField()
    so_luong = models.FloatField()
    ho_khach_hang = models.CharField(max_length=200, blank=True)
    noi_tieu_thu = models.CharField(max_length=200, blank=True)
    ghi_chu = models.TextField(blank=True)

    def __str__(self):
        return f"TT {self.thanh_pham.ten_thanh_pham} - {self.ngay_tieu_thu}"

# ===== MODELS CHO TỒN KHO KZ24 =====
class TonKhoKZ24(models.Model):
    vat_tu = models.ForeignKey(DanhMucVatTu, on_delete=models.CASCADE)
    ngay = models.DateField()
    so_luong = models.FloatField(default=0.0)

    def __str__(self):
        return f"KZ24 {self.vat_tu.ten_vat_tu} - {self.ngay}"

# ===== MODELS CHO PHIẾU VẬT TƯ =====
class Phieu(models.Model):
    TRANG_THAI_CHOICES = [
        ('cho_duyet', 'Chờ duyệt'),
        ('dmv_duyet', 'DMV đã duyệt'),
        ('ch_duyet', 'CH đã duyệt'),
        ('huy', 'Đã hủy'),
    ]
    
    so_phieu = models.CharField(max_length=50, unique=True)
    ngay_tao = models.DateTimeField(auto_now_add=True)
    tai_khoan_tao = models.ForeignKey(User, on_delete=models.CASCADE)
    trang_thai = models.CharField(max_length=20, choices=TRANG_THAI_CHOICES, default='cho_duyet')
    ghi_chu = models.TextField(blank=True)

    def __str__(self):
        return f"Phiếu {self.so_phieu}"

class PhieuVatTu(models.Model):
    phieu = models.ForeignKey(Phieu, on_delete=models.CASCADE)
    ten_vat_tu = models.CharField(max_length=200)
    don_vi_tinh = models.CharField(max_length=50)
    so_luong = models.FloatField()
    ghi_chu = models.TextField(blank=True)

    def __str__(self):
        return f"{self.phieu.so_phieu} - {self.ten_vat_tu}"

class LogDuyet(models.Model):
    phieu = models.ForeignKey(Phieu, on_delete=models.CASCADE)
    nguoi_duyet = models.ForeignKey(User, on_delete=models.CASCADE)
    thoi_gian_duyet = models.DateTimeField(auto_now_add=True)
    hanh_dong = models.CharField(max_length=50)  # 'duyet_dmv', 'duyet_ch', 'huy'
    ghi_chu = models.TextField(blank=True)

    def __str__(self):
        return f"{self.phieu.so_phieu} - {self.hanh_dong}"

# ===== MODELS CHO QUẢN LÝ NGƯỜI DÙNG =====
class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    last_password = models.CharField(max_length=128, default='123')

    def __str__(self):
        return self.user.username

class UserGroup(models.Model):
    ten_nhom = models.CharField(max_length=100)
    mo_ta = models.TextField(blank=True)
    users = models.ManyToManyField(User, blank=True)

    def __str__(self):
        return self.ten_nhom
