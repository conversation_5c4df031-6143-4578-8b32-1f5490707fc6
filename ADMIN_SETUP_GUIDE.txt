📋 HƯỚNG DẪN SETUP MÁY ADMIN - Z115 PROJECT
================================================

🎯 MỤC TIÊU:
Copy project từ máy HUNG DEP ZAI sang máy Admin và sửa lỗi migration

📂 ĐƯỜNG DẪN:
- Máy nguồn: C:\Users\<USER>\z115_project
- Máy đích:   C:\Users\<USER>\z115_project

🔄 BƯỚC 1: COPY FOLDER (Thủ công)
================================
1. Trên máy HUNG DEP ZAI:
   - Mở File Explorer
   - Đi đến C:\Users\<USER>\
   - Copy folder "z115_project" (Ctrl+C)

2. Trên máy Admin:
   - Mở File Explorer  
   - Đi đến C:\Users\<USER>\
   - Paste folder "z115_project" (Ctrl+V)
   - Đợi copy hoàn thành

🔧 BƯỚC 2: SỬA LỖI MIGRATION (Máy Admin)
=======================================
1. Mở Command Prompt:
   cd C:\Users\<USER>\z115_project

2. Chạy script sửa lỗi:
   python fix_admin_simple.py

3. Nếu script báo thành công, chuyển bước 3

🚀 BƯỚC 3: CHẠY SERVER (Máy Admin)
=================================
1. Chạy server:
   python manage.py runserver *************:8000

2. Test các trang:
   - http://*************:8000/
   - http://*************:8000/thanh-pham/
   - http://*************:8000/vat-tu-pvsx/

✅ KIỂM TRA THÀNH CÔNG:
======================
- ✅ Không có lỗi "no such table"
- ✅ Trang thành phẩm hiển thị bình thường
- ✅ Tồn kho trong kỳ hoạt động
- ✅ Cảnh báo hết hàng hoạt động
- ✅ Bộ lọc ngày hoạt động
- ✅ PHONGB12 có chế độ khóa

🔍 XỬ LÝ LỖI:
=============
Nếu vẫn có lỗi "InconsistentMigrationHistory":
1. python fix_admin_simple.py
2. python manage.py showmigrations z115_app
3. Kiểm tra tất cả migration có [X]

Nếu vẫn có lỗi "no such table":
1. Kiểm tra database đã copy đúng chưa
2. Chạy lại fix_admin_simple.py
3. Restart server

📞 HỖ TRỢ:
==========
Nếu gặp lỗi, gửi thông tin:
1. Lỗi cụ thể từ Command Prompt
2. Kết quả "python manage.py showmigrations z115_app"
3. Bước đang thực hiện

🎉 HOÀN THÀNH:
=============
Sau khi setup thành công:
- ✅ Database đã đồng bộ
- ✅ Migration đã sửa
- ✅ Server chạy bình thường
- ✅ Tất cả chức năng hoạt động

---
Tạo bởi: Augment Agent
Ngày: 05/08/2025
