#!/usr/bin/env python
"""
Script fake tất cả migrations để bỏ qua lỗi "table already exists"
"""
import subprocess
import sqlite3
import os

def fake_migrations():
    """Fake tất cả migrations"""
    print("🔄 FAKE TẤT CẢ MIGRATIONS")
    print("=" * 40)
    
    # Danh sách migrations cần fake
    migrations_to_fake = [
        '0015_danhmucthanhpham_sanxuattncn_tieuthutncn',
        '0016_nhapvattu_quy_cach', 
        '0017_tonkhokz24',
        '0018_danhmucthanhpham_phan_xuong_san_xuat_and_more'
    ]
    
    success_count = 0
    for migration in migrations_to_fake:
        cmd = f"python manage.py migrate z115_app {migration} --fake"
        print(f"\n▶️ {cmd}")
        
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=20)
            
            if result.returncode == 0:
                print("✅ Thành công")
                success_count += 1
                # Hiển thị output quan trọng
                if result.stdout.strip():
                    for line in result.stdout.strip().split('\n'):
                        if 'Applying' in line or 'FAKED' in line:
                            print(f"   {line}")
            else:
                print(f"❌ Lỗi: {result.stderr.strip()}")
                
        except subprocess.TimeoutExpired:
            print("❌ Timeout")
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return success_count

def update_migration_table_directly():
    """Cập nhật bảng migration trực tiếp"""
    print("\n🔧 Cập nhật migration table trực tiếp...")
    
    conn = sqlite3.connect("db.sqlite3")
    cursor = conn.cursor()
    
    try:
        # Thêm các migration còn thiếu vào database
        migrations_to_add = [
            '0015_danhmucthanhpham_sanxuattncn_tieuthutncn',
            '0016_nhapvattu_quy_cach',
            '0017_tonkhokz24', 
            '0018_danhmucthanhpham_phan_xuong_san_xuat_and_more'
        ]
        
        for migration in migrations_to_add:
            # Kiểm tra xem migration đã có chưa
            cursor.execute("""
                SELECT COUNT(*) FROM django_migrations 
                WHERE app = 'z115_app' AND name = ?;
            """, (migration,))
            
            if cursor.fetchone()[0] == 0:
                # Thêm migration vào database
                cursor.execute("""
                    INSERT INTO django_migrations (app, name, applied) 
                    VALUES ('z115_app', ?, datetime('now'));
                """, (migration,))
                print(f"  ✅ Thêm {migration}")
            else:
                print(f"  ✅ {migration} đã có")
        
        conn.commit()
        conn.close()
        
        print("✅ Cập nhật migration table thành công!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        conn.rollback()
        conn.close()
        return False

def verify_final_status():
    """Kiểm tra trạng thái cuối"""
    print("\n🔍 Kiểm tra trạng thái cuối...")
    
    try:
        # Kiểm tra migration status
        result = subprocess.run("python manage.py showmigrations z115_app", 
                              shell=True, capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            print("✅ Migration status:")
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if '[X]' in line or '[ ]' in line:
                    print(f"   {line}")
        
        # Test migrate
        result2 = subprocess.run("python manage.py migrate", 
                               shell=True, capture_output=True, text=True, timeout=15)
        if result2.returncode == 0:
            if "No migrations to apply" in result2.stdout:
                print("✅ Migrate: No migrations to apply")
                return True
            else:
                print("⚠️ Migrate có output khác:")
                print(f"   {result2.stdout.strip()}")
        else:
            print(f"❌ Migrate lỗi: {result2.stderr.strip()}")
            
    except Exception as e:
        print(f"❌ Lỗi verify: {e}")
    
    return False

def main():
    """Main function"""
    print("🔧 FAKE TẤT CẢ MIGRATIONS - MÁY ADMIN")
    print("=" * 50)
    
    if not os.path.exists("manage.py"):
        print("❌ Không tìm thấy manage.py")
        return
    
    if not os.path.exists("db.sqlite3"):
        print("❌ Không tìm thấy db.sqlite3")
        return
    
    print(f"📁 Thư mục: {os.getcwd()}")
    
    # Thử fake migrations trước
    success_count = fake_migrations()
    
    # Nếu fake không thành công, cập nhật trực tiếp
    if success_count < 4:
        print(f"\n⚠️ Chỉ fake được {success_count}/4 migrations")
        print("🔧 Thử cập nhật migration table trực tiếp...")
        update_migration_table_directly()
    
    # Kiểm tra kết quả cuối
    if verify_final_status():
        print("\n" + "=" * 50)
        print("✅ HOÀN THÀNH SỬA LỖI!")
        print("\n🚀 CHẠY SERVER:")
        print("python manage.py runserver 192.170.5.186:8000")
        print("\n🌐 TEST:")
        print("http://192.170.5.186:8000/thanh-pham/")
    else:
        print("\n❌ VẪN CÓ VẤN ĐỀ!")
        print("📝 Thử chạy server và test thủ công")

if __name__ == "__main__":
    main()
