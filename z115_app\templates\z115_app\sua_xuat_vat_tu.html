{% extends 'z115_app/base.html' %}
{% block title %}S<PERSON><PERSON> xu<PERSON>t vật tư{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">S<PERSON><PERSON> <PERSON><PERSON><PERSON>u xuất vật tư</h2>
    <form method="post">
        {% csrf_token %}
        <div class="mb-3">
            <label class="form-label">Ngày xu<PERSON>t kho</label>
            <input type="date" name="ngay_xuat" class="form-control" value="{{ xuat.ngay_xuat|date:'Y-m-d' }}" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Tên vật tư</label>
            <input list="vat_tu_list" name="ten_vat_tu" class="form-control" value="{{ xuat.vat_tu.ten_vat_tu }}" required>
            <datalist id="vat_tu_list">
                {% for vt in danh_muc %}
                <option value="{{ vt.ten_vat_tu }}">
                {% endfor %}
            </datalist>
        </div>
        <div class="mb-3">
            <label class="form-label">Đvt</label>
            <input type="text" name="don_vi_tinh" class="form-control" value="{{ xuat.vat_tu.don_vi_tinh }}" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Số lượng xuất</label>
            <input type="number" step="any" name="so_luong" class="form-control" value="{{ xuat.so_luong }}" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Ghi chú</label>
            <input type="text" name="ghi_chu" class="form-control" value="{{ xuat.ghi_chu }}">
        </div>
        <button type="submit" class="btn btn-success">Lưu</button>
        <a href="{% url 'xuat_vat_tu' %}" class="btn btn-secondary">Huỷ</a>
    </form>
</div>
{% endblock %}