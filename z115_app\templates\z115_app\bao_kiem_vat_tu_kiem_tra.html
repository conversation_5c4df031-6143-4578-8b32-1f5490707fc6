{% extends 'z115_app/base.html' %}

{% block title %}VẬT TƯ CHỜ KIỂM{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>VẬT TƯ CHỜ KIỂM</h2>

    {% if messages %}
    {% for message in messages %}
    <div class="alert alert-{{ message.tags }}">{{ message }}</div>
    {% endfor %}
    {% endif %}

    <form method="post" action="" id="kiemTraForm">
        {% csrf_token %}
        <input type="hidden" name="current_page" id="current_page" value="{{ page_obj.number }}">
        <table class="table table-striped table-hover">
            <thead style="background-color: #0066cc; color: white;">
                <tr>
                    <th style="padding: 10px;">Ngày</th>
                    <th style="padding: 10px;">Tên quy cách</th>
                    <th style="padding: 10px;">Dùng vào việc</th>
                    <th style="padding: 10px;">Đvt</th>
                    <th style="padding: 10px;"><PERSON><PERSON> lượng</th>
                    <th style="padding: 10px;">Nơi để vật tư</th>
                    <th style="padding: 10px;">Số hợp cách</th>
                    <th style="padding: 10px;">Kết quả kiểm tra</th>
                    <th style="padding: 10px;">Ngày trả kết quả</th>
                </tr>
            </thead>
            <tbody>
                {% for item in page_obj %}
                <tr>
                    <td style="padding: 10px;">{{ item.ngay|date:'d/m/Y' }}</td>
                    <td style="padding: 10px;">{{ item.ten_quy_cach }}</td>
                    <td style="padding: 10px;">{{ item.dung_vao_viec }}</td>
                    <td style="padding: 10px;">{{ item.don_vi_tinh }}</td>
                    <td style="padding: 10px;">{{ item.so_luong|floatformat:0 }}</td>
                    <td style="padding: 10px;">{{ item.noi_de_vat_tu }}</td>
                    <td style="padding: 10px;"><input type="number" name="so_hop_cach_{{ item.id }}"
                            value="{{ item.so_hop_cach|default:0 }}" class="form-control"></td>
                    <td style="padding: 10px;">
                        <textarea name="ket_qua_kiem_tra_{{ item.id }}" class="form-control auto-expand ket-qua-input"
                            rows="1" data-item-id="{{ item.id }}">{{ item.ket_qua_kiem_tra|default:'' }}</textarea>
                    </td>
                    <td style="padding: 10px;"><input type="date" name="ngay_tra_ket_qua_{{ item.id }}"
                            value="{{ item.ngay_tra_ket_qua|date:'Y-m-d'|default:'' }}"
                            class="form-control ngay-tra-input" id="ngay_tra_{{ item.id }}"></td>
                    <input type="hidden" name="item_id" value="{{ item.id }}">
                </tr>
                {% endfor %}
            </tbody>
        </table>
        <button type="button" id="saveTemp" class="btn btn-info">LƯU TẠM</button>
        <button type="button" id="saveAll" class="btn btn-primary">LƯU TẤT CẢ</button>
    </form>

    {% if page_obj.has_other_pages %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}">Trước</a></li>
            {% else %}
            <li class="page-item disabled"><span class="page-link">Trước</span></li>
            {% endif %}
            {% for num in page_obj.paginator.page_range %}
            <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
            </li>
            {% endfor %}
            {% if page_obj.has_next %}
            <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}">Tiếp</a></li>
            {% else %}
            <li class="page-item disabled"><span class="page-link">Tiếp</span></li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% if user.username == 'PHONGB12' %}
    <div class="text-center mt-4">
        <a href="{% url 'home' %}" class="btn btn-secondary">QUAY LẠI</a>
    </div>
    {% endif %}
</div>

<style>
    .auto-expand {
        min-height: 38px;
        overflow: hidden;
        resize: none;
    }

    .auto-expand:focus {
        outline: none;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const textareas = document.querySelectorAll('.auto-expand');
        textareas.forEach(textarea => {
            textarea.addEventListener('input', function () {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        });

        // Tự động điền ngày hiện tại khi nhập vào ô "Kết quả kiểm tra"
        const ketQuaInputs = document.querySelectorAll('.ket-qua-input');
        ketQuaInputs.forEach(input => {
            input.addEventListener('input', function () {
                const itemId = this.getAttribute('data-item-id');
                const ngayTraInput = document.getElementById('ngay_tra_' + itemId);

                // Nếu có nội dung trong ô kết quả kiểm tra và ô ngày trả chưa có giá trị
                if (this.value.trim() !== '' && ngayTraInput.value === '') {
                    // Lấy ngày hiện tại theo định dạng YYYY-MM-DD
                    const today = new Date();
                    const year = today.getFullYear();
                    const month = String(today.getMonth() + 1).padStart(2, '0');
                    const day = String(today.getDate()).padStart(2, '0');
                    const todayString = year + '-' + month + '-' + day;

                    ngayTraInput.value = todayString;
                }
            });
        });

        const form = document.getElementById('kiemTraForm');
        const saveTempBtn = document.getElementById('saveTemp');
        const saveAllBtn = document.getElementById('saveAll');

        // Lấy CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        const csrftoken = getCookie('csrftoken');

        saveTempBtn.addEventListener('click', function () {
            const formData = new FormData(form);
            formData.append('update_temp', '1'); // Thêm trường để xác định hành động
            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': csrftoken
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert(data.message);
                    } else {
                        alert('Lỗi: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Đã xảy ra lỗi khi gửi dữ liệu. Vui lòng kiểm tra console log.');
                });
        });

        saveAllBtn.addEventListener('click', function () {
            const formData = new FormData(form);
            formData.append('save_all', '1'); // Thêm trường để xác định hành động
            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': csrftoken
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert(data.message);
                        if (data.redirect) {
                            window.location.href = data.redirect;
                        }
                    } else {
                        alert('Lỗi: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Đã xảy ra lỗi khi gửi dữ liệu. Vui lòng kiểm tra console log.');
                });
        });
    });
</script>
{% endblock %}