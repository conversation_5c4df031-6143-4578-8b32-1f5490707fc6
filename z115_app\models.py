from django.db import models
from django.contrib.auth.models import User

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    last_password = models.CharField(max_length=128, default='123')

    def __str__(self):
        return self.user.username

class Dummy(models.Model):
    class Meta:
        permissions = [
            ("view_baokiem", "Can view Báo Kiểm Vật Tư PVSX"),
            ("view_kehoach", "Can view Kế Hoạch Sản Xuất"),
            ("view_thuocno", "Can view Thuốc Nổ CN"),
            ("view_cap_vat_tu_khu_a", "Can view Cấp Vật Tư PVSX Khu A"),
            ("view_nhap_kho_thanh_pham_khu_a", "Can view Nhập Kho Thà<PERSON> Phẩm, BTP Khu A"),
        ]

class BaoKiemVatTu(models.Model):
    ngay = models.DateField()
    ma_vlspp = models.Char<PERSON>ield(max_length=20, blank=True)
    ma_kho = models.CharField(max_length=20)  # Bắt buộc, không để blank=True
    ten_quy_cach = models.CharField(max_length=200)
    dung_vao_viec = models.CharField(max_length=200)
    don_vi_tinh = models.CharField(max_length=50)
    so_luong = models.FloatField(default=0.0)
    nguoi_bao_kiem = models.CharField(max_length=100)
    noi_de_vat_tu = models.CharField(max_length=100)
    so_hop_cach = models.FloatField(default=0.0, null=True, blank=True)
    ket_qua_kiem_tra = models.CharField(max_length=200, blank=True)
    ngay_tra_ket_qua = models.DateField(null=True, blank=True)
    phong_b12_ky = models.CharField(max_length=100, blank=True)
    phong_b3_ky = models.CharField(max_length=100, blank=True)

    def save(self, *args, **kwargs):
        if self.ma_vlspp:
            self.ma_kho = self.ma_vlspp[:2]  # Lấy 2 ký tự đầu
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.ten_quy_cach} - {self.ngay}"

class BaoKiemBackup(models.Model):
    ngay = models.DateField()
    ma_vlspp = models.CharField(max_length=100, blank=True)
    ma_kho = models.CharField(max_length=100, blank=True)
    ten_quy_cach = models.CharField(max_length=200)
    dung_vao_viec = models.CharField(max_length=200)
    don_vi_tinh = models.CharField(max_length=50)
    so_luong = models.FloatField(default=0.0)
    nguoi_bao_kiem = models.CharField(max_length=100)
    noi_de_vat_tu = models.CharField(max_length=200)
    so_hop_cach = models.IntegerField(null=True, blank=True)
    ket_qua_kiem_tra = models.TextField(blank=True)
    ngay_tra_ket_qua = models.DateField(null=True, blank=True)
    phong_b12_ky = models.CharField(max_length=100, blank=True)
    phong_b3_ky = models.CharField(max_length=100, blank=True)
    backup_date = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.ten_quy_cach} - {self.ngay}"

    class Meta:
        verbose_name = "Backup Báo Kiểm Vật Tư"
        verbose_name_plural = "Backups Báo Kiểm Vật Tư"

class TonKhoB3(models.Model):
    stt = models.IntegerField(null=True, blank=True)
    ma_vat_tu = models.CharField(max_length=255)
    vat_tu = models.CharField(max_length=255)
    don_vi_tinh = models.CharField(max_length=50)
    so_luong = models.FloatField(default=0.0)
    gia_tri = models.FloatField(default=0.0)
    phan_loai = models.CharField(max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.ma_vat_tu} - {self.vat_tu}"

class TonKhoB3Backup(models.Model):
    stt = models.IntegerField(null=True, blank=True)
    ma_vat_tu = models.CharField(max_length=255)
    vat_tu = models.CharField(max_length=255)
    don_vi_tinh = models.CharField(max_length=50)
    so_luong = models.FloatField(default=0.0)
    gia_tri = models.FloatField(default=0.0)
    phan_loai = models.CharField(max_length=50)
    backup_date = models.DateTimeField()

    def __str__(self):
        return f"{self.ma_vat_tu} - {self.vat_tu} (Backup)"

class UserGroup(models.Model):
    name = models.CharField(max_length=50, unique=True)
    members = models.ManyToManyField(User)

    def __str__(self):
        return self.name

class Phieu(models.Model):
    TRANG_THAI_CHOICES = [
        ('TAO_PHIEU', 'Tạo phiếu'),
        ('CHO_DMV_DUYET', 'Chờ ĐMV duyệt'),
        ('CHO_CH_DUYET', 'Chờ Chỉ huy duyệt'),
        ('DA_DUYET_CH', 'Chỉ huy đã duyệt'),
        ('CH_HUY_PHIEU', 'Chỉ huy huỷ phiếu'),
    ]

    so_phieu = models.IntegerField()
    don_vi_nhan = models.CharField(max_length=50)
    kho = models.CharField(max_length=10)
    ngay_tao = models.DateTimeField(auto_now_add=True)
    tai_khoan_tao = models.CharField(max_length=100, default='', blank=True)
    trang_thai = models.CharField(max_length=20, choices=TRANG_THAI_CHOICES)

    def __str__(self):
        return f"Phiếu {self.so_phieu}"

class PhieuVatTu(models.Model):
    phieu = models.ForeignKey(Phieu, on_delete=models.CASCADE)
    stt = models.IntegerField(null=True, blank=True)
    muc_dich_su_dung = models.CharField(max_length=100)
    ten_vat_tu = models.CharField(max_length=200)
    don_vi_tinh = models.CharField(max_length=50)
    so_luong_yeu_cau = models.FloatField()
    so_luong_duyet = models.FloatField(null=True, blank=True)
    so_luong_thuc_cap = models.FloatField(null=True, blank=True)

    def __str__(self):
        return f"{self.ten_vat_tu} - {self.phieu.so_phieu}"

class LogDuyet(models.Model):
    phieu = models.ForeignKey(Phieu, on_delete=models.CASCADE)
    nguoi_duyet = models.CharField(max_length=50)
    thoi_gian = models.DateTimeField()
    vai_tro = models.CharField(max_length=10, choices=[('DMV', 'Định mức viên'), ('CH', 'Chỉ huy')])
    ghi_chu = models.CharField(max_length=100)

    def __str__(self):
        return f"{self.vai_tro} - {self.phieu.so_phieu}"
class DanhMucVatTu(models.Model):
    ten_vat_tu = models.CharField(max_length=255)
    don_vi_tinh = models.CharField(max_length=50)
    nha_cung_cap = models.CharField(max_length=255, blank=True)
    quy_cach = models.CharField(max_length=255, blank=True)
    xuat_xu = models.CharField(max_length=255, blank=True)
    don_vi_nhan = models.CharField(max_length=255, blank=True)
    ghi_chu = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return self.ten_vat_tu

class NhapVatTu(models.Model):
    vat_tu = models.ForeignKey(DanhMucVatTu, on_delete=models.CASCADE)
    ngay_nhap = models.DateField()
    so_luong = models.FloatField()
    nha_cung_cap = models.CharField(max_length=255, blank=True)
    xuat_xu = models.CharField(max_length=255, blank=True)
    ghi_chu = models.CharField(max_length=255, blank=True)
    quy_cach = models.CharField(max_length=255, blank=True)

class XuatVatTu(models.Model):
    vat_tu = models.ForeignKey(DanhMucVatTu, on_delete=models.CASCADE)
    ngay_xuat = models.DateField()
    so_luong = models.FloatField()
    don_vi_nhan = models.CharField(max_length=255, blank=True)
    ghi_chu = models.CharField(max_length=255, blank=True)
# --- THÀNH PHẨM ---
class DanhMucThanhPham(models.Model):
    ten_thanh_pham = models.CharField(max_length=255)
    don_vi_tinh = models.CharField(max_length=50)
    ho_khach_hang = models.CharField(max_length=255, blank=True)
    noi_tieu_thu = models.CharField(max_length=255, blank=True)
    phan_xuong_san_xuat = models.CharField(max_length=255, blank=True)
    ghi_chu = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return self.ten_thanh_pham

class TieuThuTNCN(models.Model):
    thanh_pham = models.ForeignKey(DanhMucThanhPham, on_delete=models.CASCADE)
    ngay_xuat = models.DateField()
    ho_khach_hang = models.CharField(max_length=255, blank=True)
    noi_tieu_thu = models.CharField(max_length=255, blank=True)
    don_vi_tinh = models.CharField(max_length=50)
    so_luong_tieu_thu = models.FloatField()
    don_gia = models.FloatField(blank=True, null=True)
    thanh_tien = models.FloatField(blank=True, null=True)
    ghi_chu = models.CharField(max_length=255, blank=True)

class SanXuatTNCN(models.Model):
    thanh_pham = models.ForeignKey(DanhMucThanhPham, on_delete=models.CASCADE)
    ngay_san_xuat = models.DateField()
    don_vi_tinh = models.CharField(max_length=50)
    so_luong_san_xuat = models.FloatField()
    ghi_chu = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return f"{self.thanh_pham.ten_thanh_pham} - {self.ngay_san_xuat}"

class TonKhoKZ24(models.Model):
    ngay = models.DateField()
    vat_tu = models.ForeignKey(DanhMucVatTu, on_delete=models.CASCADE)
    so_luong = models.FloatField()
    ghi_chu = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return f"{self.vat_tu.ten_vat_tu} - {self.ngay}"

# --- THÀNH PHẨM PVSX ---
class SanXuatThanhPham(models.Model):
    thanh_pham = models.ForeignKey(DanhMucThanhPham, on_delete=models.CASCADE)
    ngay_san_xuat = models.DateField()
    so_luong = models.FloatField()
    phan_xuong_san_xuat = models.CharField(max_length=255, blank=True)
    ghi_chu = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return f"{self.thanh_pham.ten_thanh_pham} - {self.ngay_san_xuat}"

class TieuThuThanhPham(models.Model):
    thanh_pham = models.ForeignKey(DanhMucThanhPham, on_delete=models.CASCADE)
    ngay_tieu_thu = models.DateField()
    so_luong = models.FloatField()
    ho_khach_hang = models.CharField(max_length=255, blank=True)
    noi_tieu_thu = models.CharField(max_length=255, blank=True)
    ghi_chu = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return f"{self.thanh_pham.ten_thanh_pham} - {self.ngay_tieu_thu}"