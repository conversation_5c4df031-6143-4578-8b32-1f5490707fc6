from django.db import models
from django.contrib.auth.models import User

# Import tất cả models từ các module con
from .models.b3_models import *
from .models.b2_models import *

# Chỉ giữ lại các models không trùng lặp
class Dummy(models.Model):
    class Meta:
        permissions = [
            ("view_baokiem", "Can view Báo Kiểm Vật Tư PVSX"),
            ("view_kehoach", "Can view Kế Hoạch Sản Xuất"),
            ("view_thuocno", "Can view Thuốc Nổ CN"),
            ("view_cap_vat_tu_khu_a", "Can view Cấp Vật Tư PVSX Khu A"),
            ("view_nhap_kho_thanh_pham_khu_a", "Can view Nhập Kho Thành Phẩm, BTP Khu A"),
        ]

"""
Tất cả các models cũ đã được chuyển vào:
- z115_app/models/b3_models.py (cho PHÒNG VẬT TƯ)  
- z115_app/models/b2_models.py (cho PHÒNG TỔ CHỨC LAO ĐỘNG)

Và được import tự động ở đầu file này.
"""
