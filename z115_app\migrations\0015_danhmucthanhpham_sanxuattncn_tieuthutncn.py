# Generated by Django 5.0.7 on 2025-07-25 12:27

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('z115_app', '0014_danhmucvattu_nhapvattu_xuatvattu'),
    ]

    operations = [
        migrations.CreateModel(
            name='DanhMucThanhPham',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ten_thanh_pham', models.Char<PERSON>ield(max_length=255)),
                ('don_vi_tinh', models.CharField(max_length=50)),
                ('ho_khach_hang', models.CharField(blank=True, max_length=255)),
                ('noi_tieu_thu', models.CharField(blank=True, max_length=255)),
                ('ghi_chu', models.CharField(blank=True, max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='SanXuatTNCN',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ngay_san_xuat', models.DateField()),
                ('don_vi_tinh', models.CharField(max_length=50)),
                ('so_luong_san_xuat', models.FloatField()),
                ('ghi_chu', models.CharField(blank=True, max_length=255)),
                ('thanh_pham', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='z115_app.danhmucthanhpham')),
            ],
        ),
        migrations.CreateModel(
            name='TieuThuTNCN',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ngay_xuat', models.DateField()),
                ('ho_khach_hang', models.CharField(blank=True, max_length=255)),
                ('noi_tieu_thu', models.CharField(blank=True, max_length=255)),
                ('don_vi_tinh', models.CharField(max_length=50)),
                ('so_luong_tieu_thu', models.FloatField()),
                ('don_gia', models.FloatField(blank=True, null=True)),
                ('thanh_tien', models.FloatField(blank=True, null=True)),
                ('ghi_chu', models.CharField(blank=True, max_length=255)),
                ('thanh_pham', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='z115_app.danhmucthanhpham')),
            ],
        ),
    ]
