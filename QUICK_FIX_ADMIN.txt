🚨 SỬA NHANH LỖI "no such table: z115_app_sanxuatthanhpham"
=======================================================

🎯 VẤN ĐỀ:
Migration đã thành công nhưng bảng z115_app_sanxuatthanhpham chưa được tạo

🔧 GIẢI PHÁP:
Copy và chạy 1 trong 3 cách sau trên máy Admin:

📁 CÁCH 1: Script Python (Khuyến nghị)
=====================================
1. Copy file: fix_tables_admin.py
2. Chạy: python fix_tables_admin.py
3. Restart server

📁 CÁCH 2: File Batch (Đơn giản)
===============================
1. Copy file: fix_tables.bat
2. Double-click hoặc chạy: fix_tables.bat
3. Restart server

📁 CÁCH 3: Lệnh thủ công
=======================
Mở Python shell và chạy:

import sqlite3
conn = sqlite3.connect("db.sqlite3")
cursor = conn.cursor()

cursor.execute('''
CREATE TABLE IF NOT EXISTS "z115_app_sanxuatthanhpham" (
    "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
    "ngay_san_xuat" date NOT NULL,
    "so_luong" real NOT NULL,
    "phan_xuong_san_xuat" varchar(255) NOT NULL,
    "ghi_chu" varchar(255) NOT NULL,
    "thanh_pham_id" bigint NOT NULL
);
''')

conn.commit()
conn.close()
print("✅ Đã tạo bảng!")

🚀 SAU KHI SỬA:
===============
1. Restart server:
   python manage.py runserver *************:8000

2. Test trang:
   http://*************:8000/thanh-pham/

3. Kiểm tra:
   - ✅ Không còn lỗi "no such table"
   - ✅ Trang thành phẩm hiển thị bình thường
   - ✅ Tồn kho trong kỳ hoạt động

📝 LƯU Ý:
=========
- Chỉ cần chạy 1 lần
- Nếu vẫn lỗi, thử restart server
- Script sẽ tạo tất cả bảng thiếu cần thiết

---
Tạo: 05/08/2025
