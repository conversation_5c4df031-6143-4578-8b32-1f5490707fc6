{% extends 'z115_app/base.html' %}
{% load custom_filters %}
{% block title %}BÁO CÁO TỒN KHO VẬT TƯ{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">
        BÁO CÁO TỔNG HỢP TỒN KHO NGÀY {{ today|date:"d/m/Y" }}
    </h2>
    <form method="get" class="row g-3 mb-4">
        <div class="col-md-3">
            <label class="form-label">Từ ngày</label>
            <input type="date" name="from_date" class="form-control" value="{{ from_date }}">
        </div>
        <div class="col-md-3">
            <label class="form-label">Đến ngày</label>
            <input type="date" name="to_date" class="form-control" value="{{ to_date }}">
        </div>
        <div class="col-md-3 align-self-end">
            <button type="submit" class="btn btn-primary">Xem báo c<PERSON>o</button>
        </div>
    </form>
    <table class="table table-bordered table-striped table-sm">
        <thead class="table-success">
            <tr>
                <th>STT</th>
                <th>Tên vật tư</th>
                <th>Đvt</th>
                <th>Tồn đầu</th>
                <th>Tổng nhập</th>
                <th>Tổng xuất</th>
                <th>Tồn cuối kho BNV</th>
                <th>Tồn cuối kho KZ24 A6</th>
                <th>Tồn cuối XN1</th>
                {% if is_quan_ly %}
                <th>Hành động</th>
                {% endif %}
            </tr>
        </thead>
        <tbody>
            {% for item in ton_kho_list %}
            <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ item.ten_vat_tu }}</td>
                <td>{{ item.don_vi_tinh }}</td>
                <td>{% if item.ton_dau != 0 %}{{ item.ton_dau|dot_format }}{% else %}-{% endif %}</td>
                <td>{% if item.tong_nhap != 0 %}{{ item.tong_nhap|dot_format }}{% else %}-{% endif %}</td>
                <td>{% if item.tong_xuat != 0 %}{{ item.tong_xuat|dot_format }}{% else %}-{% endif %}</td>
                <td>{% if item.ton_cuoi_bnv != 0 %}{{ item.ton_cuoi_bnv|dot_format }}{% else %}-{% endif %}</td>
                <td>{% if item.ton_cuoi_kz24 != 0 %}{{ item.ton_cuoi_kz24|dot_format }}{% else %}-{% endif %}</td>
                <td>{% if item.ton_cuoi_xn1 != 0 %}{{ item.ton_cuoi_xn1|dot_format }}{% else %}-{% endif %}</td>
                {% if is_quan_ly %}
                <td>
                    <a href="{% url 'sua_ton_kho_vat_tu' item.id %}" class="btn btn-warning btn-sm">Sửa</a>
                    <a href="{% url 'xoa_ton_kho_vat_tu' item.id %}" class="btn btn-danger btn-sm"
                        onclick="return confirm('Bạn chắc chắn muốn xoá?');">Xoá</a>
                </td>
                {% endif %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% if is_quan_ly %}
    <a href="{% url 'them_ton_kho_vat_tu' %}" class="btn btn-primary mt-2">Thêm</a>
    {% endif %}

    <!-- Nút Quay lại -->
    <div class="mt-3">
        <a href="{% url 'vat_tu_pvsx' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> QUAY LẠI
        </a>
    </div>
</div>
{% endblock %}