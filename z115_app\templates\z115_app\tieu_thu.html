{% extends 'z115_app/base.html' %}
{% load custom_filters %}
{% block title %}TIÊU THỤ THÀNH PHẨM{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">TIÊU THỤ THÀNH PHẨM</h2>

    {% if is_quan_ly %}
    <!-- Form nhập liệu một hàng ngang -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="post" action="{% url 'them_tieu_thu' %}" id="tieuThuForm">
                        {% csrf_token %}
                        <div class="row align-items-end">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="ngay_tieu_thu">Ngày tháng:</label>
                                    <input type="date" class="form-control" id="ngay_tieu_thu" name="ngay_tieu_thu"
                                        required>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="ten_thanh_pham">Tên thành phẩm:</label>
                                    <input type="text" class="form-control" id="ten_thanh_pham" name="ten_thanh_pham"
                                        placeholder="Nhập tên thành phẩm..." autocomplete="off" required>
                                    <input type="hidden" id="thanh_pham_id" name="thanh_pham">
                                    <div id="suggestions" class="list-group position-absolute"
                                        style="z-index: 1000; display: none;"></div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label for="don_vi_tinh">Đvt:</label>
                                    <input type="text" class="form-control" id="don_vi_tinh" name="don_vi_tinh"
                                        readonly>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label for="so_luong">Số lượng:</label>
                                    <input type="number" class="form-control" id="so_luong" name="so_luong" step="0.01"
                                        min="0" required>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="ho_khach_hang">Hộ khách hàng:</label>
                                    <input type="text" class="form-control" id="ho_khach_hang" name="ho_khach_hang"
                                        placeholder="Nhập hoặc chọn hộ khách hàng..." autocomplete="off">
                                    <div id="khach_hang_suggestions" class="list-group position-absolute"
                                        style="z-index: 1000; display: none; max-height: 200px; overflow-y: auto;">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label for="noi_tieu_thu">Nơi tiêu thụ:</label>
                                    <input type="text" class="form-control" id="noi_tieu_thu" name="noi_tieu_thu"
                                        placeholder="Nhập hoặc chọn nơi tiêu thụ..." autocomplete="off">
                                    <div id="noi_tieu_thu_suggestions" class="list-group position-absolute"
                                        style="z-index: 1000; display: none; max-height: 200px; overflow-y: auto;">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label for="ghi_chu">Ghi chú:</label>
                                    <input type="text" class="form-control" id="ghi_chu" name="ghi_chu">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <button type="submit" class="btn btn-success btn-sm">
                                        <i class="fas fa-save"></i> LƯU
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <button type="button" class="btn btn-info btn-sm" onclick="clearForm()">
                                        <i class="fas fa-plus"></i> Thêm mới
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
        <table class="table table-bordered table-striped align-middle">
            <thead class="table-success sticky-top">
                <tr>
                    <th>STT</th>
                    <th>Ngày tháng</th>
                    <th>Tên thành phẩm</th>
                    <th>Đvt</th>
                    <th>Số lượng tiêu thụ</th>
                    <th>Hộ khách hàng</th>
                    <th>Nơi tiêu thụ</th>
                    <th>Ghi chú</th>
                    {% if is_quan_ly %}
                    <th>Hành động</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in tieu_thu_list %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ item.ngay_tieu_thu|date:"d/m/Y" }}</td>
                    <td>{{ item.thanh_pham.ten_thanh_pham }}</td>
                    <td>{{ item.thanh_pham.don_vi_tinh }}</td>
                    <td class="text-right">{{ item.so_luong|dot_format }}</td>
                    <td>{{ item.ho_khach_hang }}</td>
                    <td>{{ item.noi_tieu_thu }}</td>
                    <td>{{ item.ghi_chu }}</td>
                    {% if is_quan_ly %}
                    <td>
                        <a href="{% url 'sua_tieu_thu' item.id %}" class="btn btn-warning btn-sm">Sửa</a>
                        <a href="{% url 'xoa_tieu_thu' item.id %}" class="btn btn-danger btn-sm"
                            onclick="return confirm('Bạn chắc chắn muốn xoá?');">Xoá</a>
                    </td>
                    {% endif %}
                </tr>
                {% empty %}
                <tr>
                    <td colspan="{% if is_quan_ly %}9{% else %}8{% endif %}" class="text-center text-muted">
                        Không có dữ liệu tiêu thụ trong khoảng thời gian này.
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Nút Quay lại -->
        <div class="mt-3">
            <a href="{% url 'thanh_pham' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> QUAY LẠI
            </a>
        </div>
    </div>
</div>

<script>
    // Tự động điền thông tin khi chọn thành phẩm
    const danhMuc = [
        {% for tp in danh_muc %}
    {
        id: "{{ tp.id }}",
            ten: "{{ tp.ten_thanh_pham|escapejs }}",
                dvt: "{{ tp.don_vi_tinh|escapejs }}",
                    ho_khach_hang: "{{ tp.ho_khach_hang|escapejs }}",
                        noi_tieu_thu: "{{ tp.noi_tieu_thu|escapejs }}"
    },
    {% endfor %}
    ];

    // Autocomplete cho tên thành phẩm
    document.getElementById('ten_thanh_pham').addEventListener('input', function () {
        const input = this.value.toLowerCase();
        const suggestions = document.getElementById('suggestions');

        if (input.length < 1) {
            suggestions.style.display = 'none';
            return;
        }

        const filtered = danhMuc.filter(tp =>
            tp.ten.toLowerCase().includes(input)
        );

        if (filtered.length > 0) {
            suggestions.innerHTML = '';
            filtered.forEach(tp => {
                const item = document.createElement('a');
                item.className = 'list-group-item list-group-item-action';
                item.textContent = tp.ten;
                item.onclick = function () {
                    selectThanhPham(tp);
                };
                suggestions.appendChild(item);
            });
            suggestions.style.display = 'block';
        } else {
            suggestions.style.display = 'none';
        }
    });

    // Chọn thành phẩm từ gợi ý
    function selectThanhPham(tp) {
        document.getElementById('ten_thanh_pham').value = tp.ten;
        document.getElementById('thanh_pham_id').value = tp.id;
        document.getElementById('don_vi_tinh').value = tp.dvt;
        if (!document.getElementById('ho_khach_hang').value) {
            document.getElementById('ho_khach_hang').value = tp.ho_khach_hang;
        }
        if (!document.getElementById('noi_tieu_thu').value) {
            document.getElementById('noi_tieu_thu').value = tp.noi_tieu_thu;
        }
        document.getElementById('suggestions').style.display = 'none';
    }



    // Clear form
    function clearForm() {
        document.getElementById('tieuThuForm').reset();
        document.getElementById('thanh_pham_id').value = '';
        document.getElementById('suggestions').style.display = 'none';
        document.getElementById('khach_hang_suggestions').style.display = 'none';
        document.getElementById('noi_tieu_thu_suggestions').style.display = 'none';
    }

    // Autocomplete cho Hộ khách hàng
    let khachHangTimeout;
    document.getElementById('ho_khach_hang').addEventListener('input', function () {
        const input = this.value.trim();
        const suggestions = document.getElementById('khach_hang_suggestions');

        clearTimeout(khachHangTimeout);

        if (input.length < 1) {
            suggestions.style.display = 'none';
            return;
        }

        khachHangTimeout = setTimeout(() => {
            fetch(`/api/khach-hang-autocomplete/?q=${encodeURIComponent(input)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.results && data.results.length > 0) {
                        suggestions.innerHTML = '';
                        data.results.forEach(item => {
                            const element = document.createElement('a');
                            element.className = 'list-group-item list-group-item-action';
                            element.textContent = item.text;
                            element.onclick = function () {
                                document.getElementById('ho_khach_hang').value = item.value;
                                suggestions.style.display = 'none';
                            };
                            suggestions.appendChild(element);
                        });
                        suggestions.style.display = 'block';
                    } else {
                        suggestions.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    suggestions.style.display = 'none';
                });
        }, 300);
    });

    // Autocomplete cho Nơi tiêu thụ
    let noiTieuThuTimeout;
    document.getElementById('noi_tieu_thu').addEventListener('input', function () {
        const input = this.value.trim();
        const suggestions = document.getElementById('noi_tieu_thu_suggestions');

        clearTimeout(noiTieuThuTimeout);

        if (input.length < 1) {
            suggestions.style.display = 'none';
            return;
        }

        noiTieuThuTimeout = setTimeout(() => {
            fetch(`/api/noi-tieu-thu-autocomplete/?q=${encodeURIComponent(input)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.results && data.results.length > 0) {
                        suggestions.innerHTML = '';
                        data.results.forEach(item => {
                            const element = document.createElement('a');
                            element.className = 'list-group-item list-group-item-action';
                            element.textContent = item.text;
                            element.onclick = function () {
                                document.getElementById('noi_tieu_thu').value = item.value;
                                suggestions.style.display = 'none';
                            };
                            suggestions.appendChild(element);
                        });
                        suggestions.style.display = 'block';
                    } else {
                        suggestions.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    suggestions.style.display = 'none';
                });
        }, 300);
    });

    // Ẩn gợi ý khi click ra ngoài
    document.addEventListener('click', function (e) {
        if (!e.target.closest('#ten_thanh_pham') && !e.target.closest('#suggestions')) {
            document.getElementById('suggestions').style.display = 'none';
        }
        if (!e.target.closest('#ho_khach_hang') && !e.target.closest('#khach_hang_suggestions')) {
            document.getElementById('khach_hang_suggestions').style.display = 'none';
        }
        if (!e.target.closest('#noi_tieu_thu') && !e.target.closest('#noi_tieu_thu_suggestions')) {
            document.getElementById('noi_tieu_thu_suggestions').style.display = 'none';
        }
    });


</script>
{% endblock %}