{% extends 'z115_app/base.html' %}
{% load custom_filters %}
{% block title %}XUẤT VẬT TƯ{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">XUẤT VẬT TƯ</h2>
    {% if is_quan_ly %}
    <form method="post" class="row g-3 mb-4">
        {% csrf_token %}
        <div class="col-md-2">
            <input type="date" name="ngay_xuat" class="form-control" required>
        </div>
        <div class="col-md-3">
            <input list="vat_tu_list" name="ten_vat_tu" class="form-control" placeholder="Tên vật tư" required
                autocomplete="off" oninput="fillVatTuInfo(this.value)">
            <datalist id="vat_tu_list">
                {% for vt in danh_muc %}
                <option value="{{ vt.ten_vat_tu }}">
                    {% endfor %}
            </datalist>
        </div>
        <div class="col-md-1">
            <input type="text" name="don_vi_tinh" id="don_vi_tinh" class="form-control" placeholder="Đvt" required>
        </div>
        <div class="col-md-2">
            <input type="number" step="any" name="so_luong" class="form-control" placeholder="Số lượng xuất" required>
        </div>
        <div class="col-md-3">
            <input type="text" name="ghi_chu" class="form-control" placeholder="Ghi chú">
        </div>
        <div class="col-md-12 text-end">
            <button type="submit" class="btn btn-success">Lưu</button>
        </div>
    </form>
    {% endif %}
    <h5 class="mb-3">Danh sách xuất vật tư</h5>
    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
        <table class="table table-bordered table-striped table-sm">
            <thead class="table-warning sticky-top">
                <tr>
                    <th>STT</th>
                    <th>Ngày xuất</th>
                    <th>Tên vật tư</th>
                    <th>Đvt</th>
                    <th>Số lượng xuất</th>
                    <th>Ghi chú</th>
                    {% if is_quan_ly %}
                    <th>Hành động</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in xuat_list %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ item.ngay_xuat|date:"d/m/Y" }}</td>
                    <td>{{ item.vat_tu.ten_vat_tu }}</td>
                    <td>{{ item.vat_tu.don_vi_tinh }}</td>
                    <td>{{ item.so_luong|dot_format }}</td>
                    <td>{{ item.ghi_chu }}</td>
                    {% if is_quan_ly %}
                    <td>
                        <a href="{% url 'sua_xuat_vat_tu' item.id %}" class="btn btn-warning btn-sm">Sửa</a>
                        <a href="{% url 'xoa_xuat_vat_tu' item.id %}" class="btn btn-danger btn-sm"
                            onclick="return confirm('Bạn chắc chắn muốn xoá?');">Xoá</a>
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% if is_quan_ly %}
    <a href="{% url 'xuat_vat_tu' %}" class="btn btn-primary mt-2">Thêm</a>
    {% endif %}

    <!-- Nút Quay lại -->
    <div class="mt-3">
        <a href="{% url 'vat_tu_pvsx' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> QUAY LẠI
        </a>
    </div>
</div>
<script>
    // Tự động điền Đvt khi chọn tên vật tư
    const danhMuc = [
        {% for vt in danh_muc %}
    {
        ten: "{{ vt.ten_vat_tu|escapejs }}",
            dvt: "{{ vt.don_vi_tinh|escapejs }}"
    },
    {% endfor %}
    ];
    function fillVatTuInfo(val) {
        const found = danhMuc.find(vt => vt.ten === val);
        if (found) {
            document.getElementById('don_vi_tinh').value = found.dvt;
        }
    }
</script>
{% endblock %}