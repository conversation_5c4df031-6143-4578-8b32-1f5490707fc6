<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script>{% extends 'z115_app/base.html' %}
{% load static %}

{% block title %}BÁO KIỂM VẬT TƯ{% endblock %}

{% block content %}
    <div class="container mt-4">
        <h2>BÁO KIỂM VẬT TƯ</h2>

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        <form method="get" action="">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="from_date">TỪ NGÀY:</label>
                    <input type="date" name="from_date" id="from_date" value="{% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}" class="form-control">
                </div>
                <div class="col-md-3">
                    <label for="to_date">ĐẾN NGÀY:</label>
                    <input type="date" name="to_date" id="to_date" value="{% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}" class="form-control">
                </div>
                <div class="col-md-2">
                    <label> </label>
                    <button type="submit" name="clear_date" value="1" class="btn btn-secondary w-100">BỎ LỌC THEO NGÀY</button>
                </div>
                <div class="col-md-4">
                    <label for="search_name">TÌM KIẾM TÊN VẬT TƯ:</label>
                    <input type="text" name="search_name" id="search_name" value="{{ search_name|default:'' }}" class="form-control" placeholder="Nhập tên vật tư...">
                </div>
            </div>
            <div class="row g-3 mt-2">
                <div class="col-md-3">
                    <label for="search_kho">TÌM KIẾM MÃ KHO:</label>
                    <input type="text" name="search_kho" id="search_kho" value="{{ search_kho|default:'' }}" class="form-control" placeholder="Nhập mã kho...">
                </div>
                <div class="col-md-3">
                    <label for="filter_type">LỌC THEO TRẠNG THÁI:</label>
                    <select name="filter_type" id="filter_type" class="form-control">
                        <option value="all" {% if filter_type == 'all' %}selected{% endif %}>Tất cả</option>
                        <option value="checked" {% if filter_type == 'checked' %}selected{% endif %}>Đã kiểm</option>
                        <option value="unchecked" {% if filter_type == 'unchecked' %}selected{% endif %}>Chưa kiểm</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label> </label>
                    <button type="submit" class="btn btn-primary w-100">TÌM KIẾM</button>
                </div>
                <div class="col-md-2">
                    <label> </label>
                    <button type="submit" name="export_excel" value="1" class="btn btn-success w-100">XUẤT EXCEL</button>
                </div>
            </div>
        </form>

        <h4 class="mt-4">Danh sách báo kiểm</h4>
        <table class="table table-striped table-hover">
            <thead style="background-color: #0066cc; color: white;">
                <tr>
                    <th style="padding: 10px;">Ngày</th>
                    <th style="padding: 10px;">Mã VLSPP</th>
                    <th style="padding: 10px;">Mã kho</th>
                    <th style="padding: 10px;">Tên quy cách</th>
                    <th style="padding: 10px;">Dùng vào việc</th>
                    <th style="padding: 10px;">Đvt</th>
                    <th style="padding: 10px;">Số lượng</th>
                    <th style="padding: 10px;">Người báo kiểm</th>
                    <th style="padding: 10px;">Nơi để vật tư</th>
                    <th style="padding: 10px;">Số hợp cách</th>
                    <th style="padding: 10px;">Kết quả kiểm tra</th>
                    <th style="padding: 10px;">Ngày trả kết quả</th>
                    <th style="padding: 10px;">Phòng B12 ký</th>
                    <th style="padding: 10px;">Phòng B3 ký</th>
                    {% if user.username == 'PHUONGB3' %}
                        <th style="padding: 10px;">Hành động</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in page_obj %}
                    <tr id="row-{{ item.id }}">
                        <td style="padding: 10px;">
                            <span class="display-mode">{{ item.ngay|date:'d/m/Y' }}</span>
                            {% if user.username == 'PHUONGB3' %}
                                <input type="date" class="form-control edit-mode" style="display: none;" value="{{ item.ngay|date:'Y-m-d' }}" data-field="ngay" data-id="{{ item.id }}">
                            {% endif %}
                        </td>
                        <td style="padding: 10px;">
                            <span class="display-mode">{{ item.ma_vlspp|default:'' }}</span>
                            {% if user.username == 'PHUONGB3' %}
                                <input type="text" class="form-control edit-mode" style="display: none;" value="{{ item.ma_vlspp|default:'' }}" data-field="ma_vlspp" data-id="{{ item.id }}">
                            {% endif %}
                        </td>
                        <td style="padding: 10px;">
                            <span class="display-mode">{{ item.ma_kho|default:'' }}</span>
                            {% if user.username == 'PHUONGB3' %}
                                <input type="text" class="form-control edit-mode" style="display: none;" value="{{ item.ma_kho|default:'' }}" data-field="ma_kho" data-id="{{ item.id }}">
                            {% endif %}
                        </td>
                        <td style="padding: 10px;">
                            <span class="display-mode">{{ item.ten_quy_cach }}</span>
                            {% if user.username == 'PHUONGB3' %}
                                <input type="text" class="form-control edit-mode" style="display: none;" value="{{ item.ten_quy_cach }}" data-field="ten_quy_cach" data-id="{{ item.id }}">
                            {% endif %}
                        </td>
                        <td style="padding: 10px;">
                            <span class="display-mode">{{ item.dung_vao_viec }}</span>
                            {% if user.username == 'PHUONGB3' %}
                                <input type="text" class="form-control edit-mode" style="display: none;" value="{{ item.dung_vao_viec }}" data-field="dung_vao_viec" data-id="{{ item.id }}">
                            {% endif %}
                        </td>
                        <td style="padding: 10px;">
                            <span class="display-mode">{{ item.don_vi_tinh }}</span>
                            {% if user.username == 'PHUONGB3' %}
                                <input type="text" class="form-control edit-mode" style="display: none;" value="{{ item.don_vi_tinh }}" data-field="don_vi_tinh" data-id="{{ item.id }}">
                            {% endif %}
                        </td>
                        <td style="padding: 10px;">
                            <span class="display-mode">{{ item.so_luong|floatformat:0 }}</span>
                            {% if user.username == 'PHUONGB3' %}
                                <input type="number" class="form-control edit-mode" style="display: none;" value="{{ item.so_luong }}" data-field="so_luong" data-id="{{ item.id }}">
                            {% endif %}
                        </td>
                        <td style="padding: 10px;">
                            <span class="display-mode">{{ item.nguoi_bao_kiem }}</span>
                            {% if user.username == 'PHUONGB3' %}
                                <input type="text" class="form-control edit-mode" style="display: none;" value="{{ item.nguoi_bao_kiem }}" data-field="nguoi_bao_kiem" data-id="{{ item.id }}">
                            {% endif %}
                        </td>
                        <td style="padding: 10px;">
                            <span class="display-mode">{{ item.noi_de_vat_tu }}</span>
                            {% if user.username == 'PHUONGB3' %}
                                <input type="text" class="form-control edit-mode" style="display: none;" value="{{ item.noi_de_vat_tu }}" data-field="noi_de_vat_tu" data-id="{{ item.id }}">
                            {% endif %}
                        </td>
                        <td style="padding: 10px;">{{ item.so_hop_cach|default:0 }}</td>
                        <td style="padding: 10px;">{{ item.ket_qua_kiem_tra|default:'' }}</td>
                        <td style="padding: 10px;">{{ item.ngay_tra_ket_qua|date:'d/m/Y'|default:'' }}</td>
                        <td style="padding: 10px;">{{ item.phong_b12_ky|default:'' }}</td>
                        <td style="padding: 10px;">{{ item.phong_b3_ky|default:'' }}</td>
                        {% if user.username == 'PHUONGB3' %}
                            <td style="padding: 10px;">
                                <button type="button" class="btn btn-warning btn-sm edit-btn" data-id="{{ item.id }}">SỬA</button>
                                <button type="button" class="btn btn-success btn-sm save-btn" data-id="{{ item.id }}" style="display: none;">LƯU</button>
                                <button type="button" class="btn btn-secondary btn-sm cancel-btn" data-id="{{ item.id }}" style="display: none;">HỦY</button>
                                <button type="button" class="btn btn-danger btn-sm delete-btn" data-id="{{ item.id }}">XÓA</button>
                            </td>
                        {% endif %}
                    </tr>
                {% endfor %}
            </tbody>
        </table>

        {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.previous_page_number }}&from_date={% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}&to_date={% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}&search_name={{ search_name|default:'' }}&search_kho={{ search_kho|default:'' }}&filter_type={{ filter_type|default:'all' }}">Trước</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Trước</span></li>
                    {% endif %}
                    {% for num in page_obj.paginator.page_range %}
                        <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                            <a class="page-link" href="?page={{ num }}&from_date={% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}&to_date={% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}&search_name={{ search_name|default:'' }}&search_kho={{ search_kho|default:'' }}&filter_type={{ filter_type|default:'all' }}">{{ num }}</a>
                        </li>
                    {% endfor %}
                    {% if page_obj.has_next %}
                        <li class="page-item"><a class="page-link" href="?page={{ page_obj.next_page_number }}&from_date={% if from_date %}{{ from_date|date:'Y-m-d' }}{% endif %}&to_date={% if to_date %}{{ to_date|date:'Y-m-d' }}{% endif %}&search_name={{ search_name|default:'' }}&search_kho={{ search_kho|default:'' }}&filter_type={{ filter_type|default:'all' }}">Tiếp</a></li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Tiếp</span></li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}

        {% if request.user.username == 'PHUONGB3' %}
            <h4 class="mt-4">Quản lý dữ liệu</h4>
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="excel_file">TẢI LÊN:</label>
                        <input type="file" name="excel_file" id="excel_file" accept=".xlsx, .xls" class="form-control" placeholder="CHỌN FILE">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" name="upload" class="btn btn-primary w-100">TẢI LÊN</button>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" name="reset_data" value="1" class="btn btn-danger w-100">RESET</button>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" name="backup_data" value="1" class="btn btn-warning w-100">SAO LƯU</button>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" name="restore_data" value="1" class="btn btn-info w-100">KHÔI PHỤC</button>
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-3">
                        <label for="phuongb3_password">Mật khẩu:</label>
                        <input type="password" name="phuongb3_password" id="phuongb3_password" class="form-control">
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-3">
                        <label for="backup_date">KHÔI PHỤC:</label>
                        <select name="backup_date" id="backup_date" class="form-control">
                            {% if backup_dates %}
                                {% for date in backup_dates %}
                                    <option value="{{ date|date:'Y-m-d H:i:s' }}">{{ date|date:'d/m/Y H:i' }}</option>
                                {% endfor %}
                            {% else %}
                                <option value="">Chưa có bản sao lưu nào.</option>
                            {% endif %}
                        </select>
                    </div>
                </div>
            </form>
        {% endif %}
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

            // Xử lý nút SỬA
            document.querySelectorAll('.edit-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const row = document.getElementById(`row-${itemId}`);
                    
                    // Ẩn display mode, hiện edit mode
                    row.querySelectorAll('.display-mode').forEach(el => el.style.display = 'none');
                    row.querySelectorAll('.edit-mode').forEach(el => el.style.display = 'block');
                    
                    // Ẩn nút SỬA/XÓA, hiện nút LƯU/HỦY
                    this.style.display = 'none';
                    row.querySelector('.delete-btn').style.display = 'none';
                    row.querySelector('.save-btn').style.display = 'inline-block';
                    row.querySelector('.cancel-btn').style.display = 'inline-block';
                });
            });

            // Xử lý nút HỦY
            document.querySelectorAll('.cancel-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const itemId = this.dataset.id;
                    const row = document.getElementById(`row-${itemId}`);
                    
                    // Hiện display mode, ẩn edit mode
                    row.querySelectorAll('.display-mode').forEach(el => el.style.display = 'inline');
                    row.querySelectorAll('.edit-mode').forEach(el => el.style.display = 'none');
                    
                    // Hiện nút SỬA/XÓA, ẩn nút LƯU/HỦY
                    row.querySelector('.edit-btn').style.display = 'inline-block';
                    row.querySelector('.delete-btn').style.display = 'inline-block';
                    this.style.display = 'none';
                    row.querySelector('.save-btn').style.display = 'none';
                });
            });

            // Xử lý nút LƯU
            document.querySelectorAll('.save-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const password = prompt('Nhập mật khẩu PHUONGB3:');
                    if (!password) {
                        alert('Vui lòng nhập mật khẩu!');
                        return;
                    }
                    
                    const itemId = this.dataset.id;
                    const row = document.getElementById(`row-${itemId}`);
                    const formData = new FormData();
                    
                    formData.append('csrfmiddlewaretoken', csrfToken);
                    formData.append('action', 'edit_item');
                    formData.append('item_id', itemId);
                    formData.append('phuongb3_password', password);
                    
                    // Lấy dữ liệu từ các input
                    row.querySelectorAll('.edit-mode').forEach(input => {
                        formData.append(input.dataset.field, input.value);
                    });
                    
                    fetch(window.location.href, {
                        method: 'POST',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => {
                        if (!response.ok) {
                            return response.text().then(text => { throw new Error(text); });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            alert('Cập nhật thành công!');
                            location.reload();
                        } else {
                            alert('Lỗi: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Có lỗi xảy ra: ' + error.message);
                    });
                });
            });

            // Xử lý nút XÓA
            document.querySelectorAll('.delete-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (confirm('Bạn có chắc chắn muốn xóa dòng này?')) {
                        const password = prompt('Nhập mật khẩu PHUONGB3:');
                        if (!password) {
                            alert('Vui lòng nhập mật khẩu!');
                            return;
                        }
                        
                        const itemId = this.dataset.id;
                        const formData = new FormData();
                        
                        formData.append('csrfmiddlewaretoken', csrfToken);
                        formData.append('action', 'delete_item');
                        formData.append('item_id', itemId);
                        formData.append('phuongb3_password', password);
                        
                        fetch(window.location.href, {
                            method: 'POST',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: formData
                        })
                        .then(response => {
                            if (!response.ok) {
                                return response.text().then(text => { throw new Error(text); });
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                alert('Xóa thành công!');
                                location.reload();
                            } else {
                                alert('Lỗi: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Có lỗi xảy ra: ' + error.message);
                        });
                    }
                });
            });
        });
    </script>
{% endblock %}