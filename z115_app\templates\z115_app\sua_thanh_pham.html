{% extends 'z115_app/base.html' %}
{% block title %}SỬA THÀNH PHẨM{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">SỬA THÀNH PHẨM</h2>
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning">
                    <h4 class="card-title mb-0"><i class="fas fa-edit"></i> Chỉnh sửa thông tin thành phẩm</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="form-group">
                            <label for="ten_thanh_pham">Tên thành phẩm:</label>
                            <input type="text" class="form-control" id="ten_thanh_pham" name="ten_thanh_pham" 
                                   value="{{ thanh_pham.ten_thanh_pham }}">
                        </div>
                        <div class="form-group">
                            <label for="don_vi_tinh">Đơn vị tính:</label>
                            <input type="text" class="form-control" id="don_vi_tinh" name="don_vi_tinh" 
                                   value="{{ thanh_pham.don_vi_tinh }}">
                        </div>
                        <div class="form-group">
                            <label for="ho_khach_hang">Hộ khách hàng:</label>
                            <input type="text" class="form-control" id="ho_khach_hang" name="ho_khach_hang" 
                                   value="{{ thanh_pham.ho_khach_hang }}">
                        </div>
                        <div class="form-group">
                            <label for="noi_tieu_thu">Nơi tiêu thụ:</label>
                            <input type="text" class="form-control" id="noi_tieu_thu" name="noi_tieu_thu" 
                                   value="{{ thanh_pham.noi_tieu_thu }}">
                        </div>
                        <div class="form-group">
                            <label for="phan_xuong_san_xuat">Phân xưởng sản xuất:</label>
                            <input type="text" class="form-control" id="phan_xuong_san_xuat" name="phan_xuong_san_xuat" 
                                   value="{{ thanh_pham.phan_xuong_san_xuat }}">
                        </div>
                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-warning" onclick="return validateForm()">
                                <i class="fas fa-save"></i> Cập nhật
                            </button>
                            <a href="{% url 'danh_muc_thanh_pham' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Hủy
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function validateForm() {
        // Lấy tất cả input trong form
        const tenThanhPham = document.getElementById('ten_thanh_pham').value.trim();
        const donViTinh = document.getElementById('don_vi_tinh').value.trim();
        const hoKhachHang = document.getElementById('ho_khach_hang').value.trim();
        const noiTieuThu = document.getElementById('noi_tieu_thu').value.trim();
        const phanXuongSanXuat = document.getElementById('phan_xuong_san_xuat').value.trim();
        
        // Kiểm tra có ít nhất 1 trường có dữ liệu
        const values = [tenThanhPham, donViTinh, hoKhachHang, noiTieuThu, phanXuongSanXuat];
        const hasValue = values.some(value => value !== '');
        
        if (!hasValue) {
            alert('Vui lòng nhập ít nhất một thông tin thành phẩm.');
            return false;
        }
        
        return true;
    }
</script>
{% endblock %}
