{% extends 'z115_app/base.html' %}
{% load custom_filters %}
{% block title %}DANH MỤC THÀNH PHẨM{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">DANH MỤC THÀNH PHẨM</h2>
    <div class="table-responsive">
        <table class="table table-bordered table-striped align-middle">
            <thead class="table-success">
                <tr>
                    <th>STT</th>
                    <th>Tên thành phẩm</th>
                    <th>Đvt</th>
                    <th>Hộ khách hàng</th>
                    <th>Nơi tiêu thụ</th>
                    <th>Phân xưởng sản xuất</th>
                    {% if is_quan_ly %}
                    <th>Hành động</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for tp in danh_muc %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ tp.ten_thanh_pham }}</td>
                    <td>{{ tp.don_vi_tinh }}</td>
                    <td>{{ tp.ho_khach_hang }}</td>
                    <td>{{ tp.noi_tieu_thu }}</td>
                    <td>{{ tp.phan_xuong_san_xuat }}</td>
                    {% if is_quan_ly %}
                    <td>
                        <a href="{% url 'sua_thanh_pham' tp.id %}" class="btn btn-warning btn-sm">Sửa</a>
                        <a href="{% url 'xoa_thanh_pham' tp.id %}" class="btn btn-danger btn-sm"
                            onclick="return confirm('Bạn chắc chắn muốn xoá?');">Xoá</a>
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
                {% if is_quan_ly %}
                <!-- Dòng thêm mới -->
                <tr id="add-row" style="display:none;">
                    <form method="post" action="{% url 'them_thanh_pham' %}">
                        {% csrf_token %}
                        <td>#</td>
                        <td><input type="text" name="ten_thanh_pham" class="form-control"></td>
                        <td><input type="text" name="don_vi_tinh" class="form-control"></td>
                        <td><input type="text" name="ho_khach_hang" class="form-control"></td>
                        <td><input type="text" name="noi_tieu_thu" class="form-control"></td>
                        <td><input type="text" name="phan_xuong_san_xuat" class="form-control"></td>
                        <td>
                            <button type="submit" class="btn btn-success btn-sm" onclick="return validateForm()">Lưu</button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="hideAddRow()">Huỷ</button>
                        </td>
                    </form>
                </tr>
                {% endif %}
            </tbody>
        </table>
        {% if is_quan_ly %}
        <div class="mt-3">
            <button type="button" class="btn btn-primary" onclick="showAddRow()">
                <i class="fas fa-plus"></i> Thêm
            </button>
        </div>
        {% endif %}

        <!-- Nút Quay lại -->
        <div class="mt-3">
            <a href="{% url 'thanh_pham' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> QUAY LẠI
            </a>
        </div>
    </div>
</div>

<script>
    function showAddRow() {
        document.getElementById('add-row').style.display = 'table-row';
    }

    function hideAddRow() {
        document.getElementById('add-row').style.display = 'none';
        // Clear all inputs
        const inputs = document.querySelectorAll('#add-row input[type="text"]');
        inputs.forEach(input => input.value = '');
    }

    function validateForm() {
        // Lấy tất cả input trong form thêm thành phẩm
        const tenThanhPham = document.querySelector('input[name="ten_thanh_pham"]');
        const donViTinh = document.querySelector('input[name="don_vi_tinh"]');
        const hoKhachHang = document.querySelector('input[name="ho_khach_hang"]');
        const noiTieuThu = document.querySelector('input[name="noi_tieu_thu"]');
        const phanXuongSanXuat = document.querySelector('input[name="phan_xuong_san_xuat"]');
        
        // Kiểm tra có ít nhất 1 input có giá trị
        const values = [
            tenThanhPham ? tenThanhPham.value.trim() : '',
            donViTinh ? donViTinh.value.trim() : '',
            hoKhachHang ? hoKhachHang.value.trim() : '',
            noiTieuThu ? noiTieuThu.value.trim() : '',
            phanXuongSanXuat ? phanXuongSanXuat.value.trim() : ''
        ];
        
        const hasValue = values.some(value => value !== '');
        
        if (!hasValue) {
            alert('Vui lòng nhập ít nhất một thông tin thành phẩm.');
            return false;
        }
        
        return true;
    }
</script>
{% endblock %}
