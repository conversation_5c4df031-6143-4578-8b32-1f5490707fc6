from django.shortcuts import render, redirect, get_object_or_404, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import HttpResponseForbidden, JsonResponse
import logging
from django.contrib import messages
from z115_app.models import (
    DanhMucVatTu, NhapVatTu, XuatVatTu, DanhMucThanh<PERSON>ham,
    TonKhoKZ24, San<PERSON><PERSON>tThanh<PERSON>ham, TieuThuThanh<PERSON>ham
)
from datetime import date, timedelta

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@login_required
def XNT_kho_xi_nghiep_1(request):
    logger.debug(f"Checking permissions for {request.user.username}: {request.user.get_all_permissions()}")
    if not request.user.has_perm('z115_app.view_thuocno'):
        logger.debug(f"Access denied for {request.user.username} to XNT_kho_xi_nghiep_1.")
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP")
    return render(request, 'z115_app/XNT_kho_xi_nghiep_1.html')

@login_required
def vat_tu_pvsx(request):
    logger.debug(f"Checking permissions for {request.user.username}: {request.user.get_all_permissions()}")
    if not request.user.has_perm('z115_app.view_thuocno'):
        logger.debug(f"Access denied for {request.user.username} to vat_tu_pvsx.")
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP")

    # Lấy dữ liệu dashboard
    today = date.today()
    # Mặc định từ ngày là ngày đầu tháng hiện tại
    first_day_of_month = today.replace(day=1)
    from_date_str = request.GET.get('from_date', first_day_of_month.strftime('%Y-%m-%d'))
    to_date_str = request.GET.get('to_date', today.strftime('%Y-%m-%d'))

    # Convert string to date object for template display
    try:
        from datetime import datetime
        from_date = datetime.strptime(from_date_str, '%Y-%m-%d').date()
        to_date = datetime.strptime(to_date_str, '%Y-%m-%d').date()
    except ValueError:
        from_date = first_day_of_month
        to_date = today
        from_date_str = from_date.strftime('%Y-%m-%d')
        to_date_str = to_date.strftime('%Y-%m-%d')

    # Nhập vật tư trong khoảng thời gian
    nhap_today = NhapVatTu.objects.filter(
        ngay_nhap__gte=from_date_str,
        ngay_nhap__lte=to_date_str
    ).select_related('vat_tu').order_by('-ngay_nhap')

    # Xuất vật tư trong khoảng thời gian
    xuat_today = XuatVatTu.objects.filter(
        ngay_xuat__gte=from_date_str,
        ngay_xuat__lte=to_date_str
    ).select_related('vat_tu').order_by('-ngay_xuat')

    # Thống kê nhà cung cấp
    nha_cung_cap_stats = {}
    nha_cung_cap_totals = {}
    for nhap in nhap_today:
        ncc = nhap.nha_cung_cap or 'Không xác định'
        if ncc not in nha_cung_cap_stats:
            nha_cung_cap_stats[ncc] = []
            nha_cung_cap_totals[ncc] = 0
        nha_cung_cap_stats[ncc].append({
            'ten_vat_tu': nhap.vat_tu.ten_vat_tu,
            'so_luong': nhap.so_luong,
            'don_vi_tinh': nhap.vat_tu.don_vi_tinh,
            'ngay_nhap': nhap.ngay_nhap
        })
        nha_cung_cap_totals[ncc] += float(nhap.so_luong)

    # Thống kê theo từng loại vật tư
    vat_tu_stats = {}
    for nhap in nhap_today:
        ten_vt = nhap.vat_tu.ten_vat_tu
        if ten_vt not in vat_tu_stats:
            vat_tu_stats[ten_vt] = {
                'don_vi_tinh': nhap.vat_tu.don_vi_tinh,
                'tong_so_luong': 0,
                'nha_cung_cap': {}
            }

        # Cộng tổng số lượng
        vat_tu_stats[ten_vt]['tong_so_luong'] += float(nhap.so_luong)

        # Thống kê theo nhà cung cấp
        ncc = nhap.nha_cung_cap or 'Không xác định'
        if ncc not in vat_tu_stats[ten_vt]['nha_cung_cap']:
            vat_tu_stats[ten_vt]['nha_cung_cap'][ncc] = []

        vat_tu_stats[ten_vt]['nha_cung_cap'][ncc].append({
            'so_luong': nhap.so_luong,
            'ngay_nhap': nhap.ngay_nhap
        })

    # Thống kê xuất theo đơn vị
    don_vi_xuat_stats = {}
    for xuat in xuat_today:
        don_vi = xuat.ghi_chu or 'Không xác định'
        if don_vi not in don_vi_xuat_stats:
            don_vi_xuat_stats[don_vi] = []
        don_vi_xuat_stats[don_vi].append({
            'ten_vat_tu': xuat.vat_tu.ten_vat_tu,
            'so_luong': xuat.so_luong,
            'don_vi_tinh': xuat.vat_tu.don_vi_tinh,
            'ngay_xuat': xuat.ngay_xuat
        })

    # Tính tồn kho và cảnh báo hết hàng (sử dụng logic giống ton_kho_vat_tu)
    # Chỉ lấy vật tư có tên vật tư (không rỗng)
    danh_muc = DanhMucVatTu.objects.filter(
        ten_vat_tu__isnull=False,
        ten_vat_tu__gt=''
    ).exclude(ten_vat_tu__exact='')
    canh_bao_het_hang = []

    for vt in danh_muc:
        # Tính tồn kho BNV đến to_date (tất cả nhập/xuất đến to_date)
        qs_nhap = NhapVatTu.objects.filter(vat_tu=vt, ngay_nhap__lte=to_date_str)
        qs_xuat = XuatVatTu.objects.filter(vat_tu=vt, ngay_xuat__lte=to_date_str)
        tong_nhap = sum([n.so_luong for n in qs_nhap])
        tong_xuat = sum([x.so_luong for x in qs_xuat])
        ton_cuoi_bnv = tong_nhap - tong_xuat

        # Lấy tồn cuối kho KZ24 mới nhất <= to_date
        from datetime import datetime
        to_date_obj = datetime.strptime(to_date_str, '%Y-%m-%d').date()
        ton_kz24_obj = TonKhoKZ24.objects.filter(
            vat_tu=vt,
            ngay__lte=to_date_obj
        ).order_by('-ngay').first()
        ton_cuoi_kz24 = ton_kz24_obj.so_luong if ton_kz24_obj else 0

        # Tồn cuối XN1 = tồn cuối BNV + tồn cuối KZ24
        ton_cuoi_xn1 = ton_cuoi_bnv + ton_cuoi_kz24

        # Cảnh báo nếu tồn kho < 1000 và có tên vật tư
        if ton_cuoi_xn1 < 1000 and vt.ten_vat_tu and vt.ten_vat_tu.strip():
            canh_bao_het_hang.append({
                'ten_vat_tu': vt.ten_vat_tu,
                'ton_kho': ton_cuoi_xn1,
                'don_vi_tinh': vt.don_vi_tinh
            })

    # Tính tồn kho chi tiết (sử dụng logic giống ton_kho_vat_tu)
    # Sử dụng cùng danh_muc đã được lọc (chỉ vật tư có tên)
    ton_kho_data = []
    for vt in danh_muc:
        # Tính tồn đầu (tất cả nhập/xuất trước from_date)
        nhap_truoc = NhapVatTu.objects.filter(vat_tu=vt, ngay_nhap__lt=from_date_str)
        xuat_truoc = XuatVatTu.objects.filter(vat_tu=vt, ngay_xuat__lt=from_date_str)
        ton_dau = sum([n.so_luong for n in nhap_truoc]) - sum([x.so_luong for x in xuat_truoc])

        # Tổng nhập, xuất trong kỳ (from_date đến to_date)
        qs_nhap_ky = NhapVatTu.objects.filter(vat_tu=vt, ngay_nhap__gte=from_date_str, ngay_nhap__lte=to_date_str)
        qs_xuat_ky = XuatVatTu.objects.filter(vat_tu=vt, ngay_xuat__gte=from_date_str, ngay_xuat__lte=to_date_str)
        tong_nhap_ky = sum([n.so_luong for n in qs_nhap_ky])
        tong_xuat_ky = sum([x.so_luong for x in qs_xuat_ky])
        ton_cuoi_bnv = ton_dau + tong_nhap_ky - tong_xuat_ky

        # Lấy tồn cuối kho KZ24 mới nhất <= to_date
        ton_kz24_obj = TonKhoKZ24.objects.filter(
            vat_tu=vt,
            ngay__lte=to_date
        ).order_by('-ngay').first()
        ton_cuoi_kz24 = ton_kz24_obj.so_luong if ton_kz24_obj else 0

        # Tồn cuối XN1 = tồn cuối BNV + tồn cuối KZ24
        ton_cuoi_xn1 = ton_cuoi_bnv + ton_cuoi_kz24

        # Chỉ thêm vào danh sách nếu có tên vật tư và có ít nhất 1 cột khác 0
        if (vt.ten_vat_tu and vt.ten_vat_tu.strip() and
            (ton_dau != 0 or tong_nhap_ky != 0 or tong_xuat_ky != 0 or
            ton_cuoi_bnv != 0 or ton_cuoi_kz24 != 0 or ton_cuoi_xn1 != 0)):
            ton_kho_data.append({
                'ten_vat_tu': vt.ten_vat_tu,
                'don_vi_tinh': vt.don_vi_tinh,
                'ton_dau': ton_dau,
                'tong_nhap': tong_nhap_ky,
                'tong_xuat': tong_xuat_ky,
                'ton_cuoi_bnv': ton_cuoi_bnv,
                'ton_cuoi_kz24': ton_cuoi_kz24,
                'ton_cuoi_xn1': ton_cuoi_xn1,
            })

    # Dữ liệu cho biểu đồ
    chart_data = {
        'nhap_labels': [item.vat_tu.ten_vat_tu for item in nhap_today[:10]],
        'nhap_data': [float(item.so_luong) for item in nhap_today[:10]],
        'xuat_labels': [item.vat_tu.ten_vat_tu for item in xuat_today[:10]],
        'xuat_data': [float(item.so_luong) for item in xuat_today[:10]],
    }

    context = {
        'today': today,
        'from_date': from_date,  # date object for template display
        'to_date': to_date,      # date object for template display
        'from_date_str': from_date_str,  # string for form input
        'to_date_str': to_date_str,      # string for form input
        'nhap_today': nhap_today,
        'xuat_today': xuat_today,
        'nha_cung_cap_stats': nha_cung_cap_stats,
        'nha_cung_cap_totals': nha_cung_cap_totals,
        'vat_tu_stats': vat_tu_stats,  # Thống kê theo từng loại vật tư
        'don_vi_xuat_stats': don_vi_xuat_stats,
        'ton_kho_data': ton_kho_data,  # Dữ liệu tồn kho chi tiết
        'canh_bao_het_hang': canh_bao_het_hang,
        'chart_data': chart_data,
        'total_nhap': sum([float(item.so_luong) for item in nhap_today]),
        'total_xuat': sum([float(item.so_luong) for item in xuat_today]),
        'total_ton_kho': len([item for item in ton_kho_data if item['ton_cuoi_xn1'] > 0]),
        'total_nha_cung_cap': len(nha_cung_cap_stats),
        'total_don_vi_xuat': len(don_vi_xuat_stats),
    }

    return render(request, 'z115_app/vat_tu_pvsx.html', context)

@login_required
def danh_sach_vat_tu(request):
    danh_muc = DanhMucVatTu.objects.all()
    is_super_hungpc892 = request.user.username == "hungpc892"
    return render(request, 'z115_app/danh_sach_vat_tu.html', {
        'danh_muc': danh_muc,
        'is_super_hungpc892': is_super_hungpc892,
    })

@login_required
def nhap_vat_tu(request):
    danh_muc = DanhMucVatTu.objects.all()
    nhap_list = NhapVatTu.objects.select_related('vat_tu').all()
    is_quan_ly = request.user.username in ["hungpc892", "HUYNHBNV"]
    if request.method == "POST" and is_quan_ly:
        ten_vat_tu = request.POST.get("ten_vat_tu")
        vat_tu = DanhMucVatTu.objects.filter(ten_vat_tu=ten_vat_tu).first()
        if vat_tu:
            NhapVatTu.objects.create(
                vat_tu=vat_tu,
                ngay_nhap=request.POST.get("ngay_nhap"),
                so_luong=request.POST.get("so_luong"),
                nha_cung_cap=request.POST.get("nha_cung_cap"),
                xuat_xu=request.POST.get("xuat_xu"),
                quy_cach=request.POST.get("quy_cach"),
            )
            messages.success(request, "Đã thêm phiếu nhập vật tư.")
            return redirect('nhap_vat_tu')
    return render(request, 'z115_app/nhap_vat_tu.html', {
        'danh_muc': danh_muc,
        'nhap_list': nhap_list,
        'is_quan_ly': is_quan_ly,
    })
# --- Nhập vật tư (thêm/sửa/xoá) ---
@login_required
def them_nhap_vat_tu(request):
    if request.user.username not in ["hungpc892", "HUYNHBNV"]:
        return HttpResponseForbidden("Bạn không có quyền thêm nhập vật tư.")
    # Có thể dùng chung form với nhap_vat_tu hoặc tách riêng nếu muốn
    return redirect('nhap_vat_tu')

@login_required
def sua_nhap_vat_tu(request, nhap_id):
    if request.user.username not in ["hungpc892", "HUYNHBNV"]:
        return HttpResponseForbidden("Bạn không có quyền sửa nhập vật tư.")
    nhap = get_object_or_404(NhapVatTu, id=nhap_id)
    if request.method == "POST":
        ten_vat_tu = request.POST.get("ten_vat_tu")
        vat_tu = DanhMucVatTu.objects.filter(ten_vat_tu=ten_vat_tu).first()
        if vat_tu:
            nhap.vat_tu = vat_tu
            nhap.ngay_nhap = request.POST.get("ngay_nhap")
            nhap.so_luong = request.POST.get("so_luong")
            nhap.nha_cung_cap = request.POST.get("nha_cung_cap") or ""
            nhap.xuat_xu = request.POST.get("xuat_xu") or ""
            nhap.quy_cach = request.POST.get("quy_cach") or ""
            nhap.save()
            messages.success(request, "Đã cập nhật phiếu nhập vật tư.")
            return redirect('nhap_vat_tu')
    return render(request, 'z115_app/sua_nhap_vat_tu.html', {'nhap': nhap})

@login_required
def xoa_nhap_vat_tu(request, nhap_id):
    if request.user.username not in ["hungpc892", "HUYNHBNV"]:
        return HttpResponseForbidden("Bạn không có quyền xoá nhập vật tư.")
    nhap = get_object_or_404(NhapVatTu, id=nhap_id)
    nhap.delete()
    messages.success(request, "Đã xoá phiếu nhập vật tư.")
    return redirect('nhap_vat_tu')

@login_required
def xuat_vat_tu(request):
    danh_muc = DanhMucVatTu.objects.all()
    xuat_list = XuatVatTu.objects.select_related('vat_tu').all()
    is_quan_ly = request.user.username in ["hungpc892", "HUYNHBNV"]
    if request.method == "POST" and is_quan_ly:
        ten_vat_tu = request.POST.get("ten_vat_tu")
        vat_tu = DanhMucVatTu.objects.filter(ten_vat_tu=ten_vat_tu).first()
        if vat_tu:
            XuatVatTu.objects.create(
                vat_tu=vat_tu,
                ngay_xuat=request.POST.get("ngay_xuat"),
                so_luong=request.POST.get("so_luong"),
                ghi_chu=request.POST.get("ghi_chu"),
            )
            messages.success(request, "Đã thêm phiếu xuất vật tư.")
            return redirect('xuat_vat_tu')
    return render(request, 'z115_app/xuat_vat_tu.html', {
        'danh_muc': danh_muc,
        'xuat_list': xuat_list,
        'is_quan_ly': is_quan_ly,
    })

@login_required
def them_xuat_vat_tu(request):
    if request.user.username not in ["hungpc892", "HUYNHBNV"]:
        return HttpResponseForbidden("Bạn không có quyền thêm xuất vật tư.")
    # Có thể dùng chung form với xuat_vat_tu hoặc tách riêng nếu muốn
    return redirect('xuat_vat_tu')

@login_required
def sua_xuat_vat_tu(request, xuat_id):
    if request.user.username not in ["hungpc892", "HUYNHBNV"]:
        return HttpResponseForbidden("Bạn không có quyền sửa xuất vật tư.")
    xuat = get_object_or_404(XuatVatTu, id=xuat_id)
    if request.method == "POST":
        ten_vat_tu = request.POST.get("ten_vat_tu")
        vat_tu = DanhMucVatTu.objects.filter(ten_vat_tu=ten_vat_tu).first()
        if vat_tu:
            xuat.vat_tu = vat_tu
            xuat.ngay_xuat = request.POST.get("ngay_xuat")
            xuat.so_luong = request.POST.get("so_luong")
            xuat.ghi_chu = request.POST.get("ghi_chu")
            xuat.save()
            messages.success(request, "Đã cập nhật phiếu xuất vật tư.")
            return redirect('xuat_vat_tu')
    return render(request, 'z115_app/sua_xuat_vat_tu.html', {'xuat': xuat})

@login_required
def xoa_xuat_vat_tu(request, xuat_id):
    if request.user.username not in ["hungpc892", "HUYNHBNV"]:
        return HttpResponseForbidden("Bạn không có quyền xoá xuất vật tư.")
    xuat = get_object_or_404(XuatVatTu, id=xuat_id)
    xuat.delete()
    messages.success(request, "Đã xoá phiếu xuất vật tư.")
    return redirect('xuat_vat_tu')

# --- TỒN KHO VẬT TƯ ---
@login_required
def ton_kho_vat_tu(request):
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    today = date.today()
    # Chỉ lấy vật tư có tên vật tư (không rỗng)
    danh_muc = DanhMucVatTu.objects.filter(
        ten_vat_tu__isnull=False,
        ten_vat_tu__gt=''
    ).exclude(ten_vat_tu__exact='')
    ton_kho_list = []
    is_quan_ly = request.user.username in ["hungpc892", "HUYNHBNV"]

    # Xác định tháng trước liền kề
    if from_date:
        from_date_obj = date.fromisoformat(from_date)
        first_day_this_month = from_date_obj.replace(day=1)
        last_month = first_day_this_month - timedelta(days=1)
        first_day_last_month = last_month.replace(day=1)
        last_day_last_month = last_month
    else:
        first_day_this_month = today.replace(day=1)
        last_month = first_day_this_month - timedelta(days=1)
        first_day_last_month = last_month.replace(day=1)
        last_day_last_month = last_month

    for vt in danh_muc:
        # Tính tồn đầu kỳ (tất cả nhập/xuất trước from_date)
        if from_date:
            nhap_truoc = NhapVatTu.objects.filter(vat_tu=vt, ngay_nhap__lt=from_date)
            xuat_truoc = XuatVatTu.objects.filter(vat_tu=vt, ngay_xuat__lt=from_date)
        else:
            # Nếu không có from_date, tính tồn đầu tháng hiện tại
            nhap_truoc = NhapVatTu.objects.filter(
                vat_tu=vt,
                ngay_nhap__gte=first_day_last_month,
                ngay_nhap__lte=last_day_last_month
            )
            xuat_truoc = XuatVatTu.objects.filter(
                vat_tu=vt,
                ngay_xuat__gte=first_day_last_month,
                ngay_xuat__lte=last_day_last_month
            )
        ton_dau = sum([n.so_luong for n in nhap_truoc]) - sum([x.so_luong for x in xuat_truoc])

        # Tổng nhập, xuất trong kỳ
        qs_nhap = NhapVatTu.objects.filter(vat_tu=vt)
        qs_xuat = XuatVatTu.objects.filter(vat_tu=vt)
        if from_date:
            qs_nhap = qs_nhap.filter(ngay_nhap__gte=from_date)
            qs_xuat = qs_xuat.filter(ngay_xuat__gte=from_date)
        if to_date:
            qs_nhap = qs_nhap.filter(ngay_nhap__lte=to_date)
            qs_xuat = qs_xuat.filter(ngay_xuat__lte=to_date)
        tong_nhap = sum([n.so_luong for n in qs_nhap])
        tong_xuat = sum([x.so_luong for x in qs_xuat])
        ton_cuoi_bnv = ton_dau + tong_nhap - tong_xuat

        # --- Lấy tồn cuối kho KZ24 ---
        # Nếu lọc theo ngày, lấy bản ghi TonKhoKZ24 mới nhất <= to_date (nếu có), hoặc <= today nếu không có to_date
        kz24_date = to_date if to_date else today
        ton_kz24_obj = TonKhoKZ24.objects.filter(
            vat_tu=vt,
            ngay__lte=kz24_date
        ).order_by('-ngay').first()
        ton_cuoi_kz24 = ton_kz24_obj.so_luong if ton_kz24_obj else 0

        ton_cuoi_xn1 = ton_cuoi_bnv + ton_cuoi_kz24

        # Chỉ thêm vào danh sách nếu có tên vật tư và có ít nhất một số lượng khác 0
        has_non_zero = (abs(ton_dau) > 0 or abs(tong_nhap) > 0 or abs(tong_xuat) > 0 or
                       abs(ton_cuoi_bnv) > 0 or abs(ton_cuoi_kz24) > 0 or abs(ton_cuoi_xn1) > 0)
        has_name = vt.ten_vat_tu and vt.ten_vat_tu.strip()

        # Chỉ thêm nếu có tên và có ít nhất một số lượng khác 0
        if has_name and has_non_zero:
            ton_kho_list.append({
                'id': vt.id,
                'ten_vat_tu': vt.ten_vat_tu,
                'don_vi_tinh': vt.don_vi_tinh,
                'ton_dau': ton_dau,
                'tong_nhap': tong_nhap,
                'tong_xuat': tong_xuat,
                'ton_cuoi_bnv': ton_cuoi_bnv,
                'ton_cuoi_kz24': ton_cuoi_kz24,
                'ton_cuoi_xn1': ton_cuoi_xn1,
            })
    return render(request, 'z115_app/ton_kho_vat_tu.html', {
        'danh_muc': danh_muc,
        'ton_kho_list': ton_kho_list,
        'is_quan_ly': is_quan_ly,
        'from_date': from_date,
        'to_date': to_date,
        'today': today,
    })
@login_required
def them_ton_kho_vat_tu(request):
    if request.user.username not in ["hungpc892", "HUYNHBNV"]:
        return HttpResponseForbidden("Bạn không có quyền thêm tồn kho.")
    # Nếu muốn thêm thủ công, code thêm ở đây
    return redirect('ton_kho_vat_tu')

@login_required
def sua_ton_kho_vat_tu(request, ton_id):
    if request.user.username not in ["hungpc892", "HUYNHBNV"]:
        return HttpResponseForbidden("Bạn không có quyền sửa tồn kho.")
    # Nếu muốn sửa thủ công, code thêm ở đây
    return redirect('ton_kho_vat_tu')

@login_required
def xoa_ton_kho_vat_tu(request, ton_id):
    if request.user.username not in ["hungpc892", "HUYNHBNV"]:
        return HttpResponseForbidden("Bạn không có quyền xoá tồn kho.")
    # Nếu muốn xoá thủ công, code thêm ở đây
    return redirect('ton_kho_vat_tu')
# --- THÀNH PHẨM ---
@login_required
def thanh_pham(request):
    logger.debug(f"Checking permissions for {request.user.username}: {request.user.get_all_permissions()}")
    if not request.user.has_perm('z115_app.view_thuocno'):
        logger.debug(f"Access denied for {request.user.username} to thanh_pham.")
        return HttpResponseForbidden("BẠN KHÔNG CÓ QUYỀN TRUY CẬP")

    # Lấy ngày từ request hoặc mặc định
    from_date_str = request.GET.get('from_date')
    to_date_str = request.GET.get('to_date')
    today = date.today()

    if not from_date_str:
        from_date = today.replace(day=1)  # Đầu tháng hiện tại
        from_date_str = from_date.strftime('%Y-%m-%d')
    else:
        from_date = date.fromisoformat(from_date_str)

    if not to_date_str:
        to_date = today
        to_date_str = to_date.strftime('%Y-%m-%d')
    else:
        to_date = date.fromisoformat(to_date_str)

    # Lấy dữ liệu sản xuất và tiêu thụ trong kỳ
    san_xuat_today = SanXuatThanhPham.objects.filter(
        ngay_san_xuat__gte=from_date_str,
        ngay_san_xuat__lte=to_date_str
    ).select_related('thanh_pham')

    tieu_thu_today = TieuThuThanhPham.objects.filter(
        ngay_tieu_thu__gte=from_date_str,
        ngay_tieu_thu__lte=to_date_str
    ).select_related('thanh_pham')

    # Tính tổng số liệu
    total_san_xuat = sum([float(item.so_luong) for item in san_xuat_today])
    total_tieu_thu = sum([float(item.so_luong) for item in tieu_thu_today])

    # Đếm khách hàng unique
    khach_hang_unique = set()
    for item in tieu_thu_today:
        if item.ho_khach_hang:
            khach_hang_unique.add(item.ho_khach_hang)
    total_khach_hang = len(khach_hang_unique)

    # Tính tồn kho và cảnh báo hết hàng - chỉ lấy thành phẩm có tên
    danh_muc = DanhMucThanhPham.objects.filter(
        ten_thanh_pham__isnull=False,
        ten_thanh_pham__gt=''
    ).exclude(ten_thanh_pham__exact='')
    canh_bao_het_hang = []
    total_ton_kho = 0

    for tp in danh_muc:
        # Tính tồn kho đến to_date
        qs_san_xuat = SanXuatThanhPham.objects.filter(thanh_pham=tp, ngay_san_xuat__lte=to_date_str)
        qs_tieu_thu = TieuThuThanhPham.objects.filter(thanh_pham=tp, ngay_tieu_thu__lte=to_date_str)
        tong_san_xuat = sum([sx.so_luong for sx in qs_san_xuat])
        tong_tieu_thu = sum([tt.so_luong for tt in qs_tieu_thu])
        ton_cuoi = tong_san_xuat - tong_tieu_thu

        if ton_cuoi > 0:
            total_ton_kho += 1

        # Cảnh báo nếu tồn kho < 1000
        if ton_cuoi < 1000:
            canh_bao_het_hang.append({
                'ten_thanh_pham': tp.ten_thanh_pham,
                'don_vi_tinh': tp.don_vi_tinh,
                'ton_cuoi': ton_cuoi
            })

    # Lấy dữ liệu theo khoảng thời gian được lọc
    san_xuat_period = SanXuatThanhPham.objects.filter(
        ngay_san_xuat__gte=from_date_str,
        ngay_san_xuat__lte=to_date_str
    ).select_related('thanh_pham')

    tieu_thu_period = TieuThuThanhPham.objects.filter(
        ngay_tieu_thu__gte=from_date_str,
        ngay_tieu_thu__lte=to_date_str
    ).select_related('thanh_pham')

    # Thống kê chi tiết cho modals
    # Dữ liệu chi tiết cho modal (full records)
    san_xuat_detail_list = san_xuat_period.order_by('-ngay_san_xuat')
    tieu_thu_detail_list = tieu_thu_period.order_by('-ngay_tieu_thu')

    # Tính toán tồn kho chi tiết cho modal (giống như trong ton_kho_thanh_pham view)
    ton_kho_detail_list = []
    for tp in danh_muc:
        # Tính tồn đầu kỳ (trước from_date)
        qs_san_xuat_truoc = SanXuatThanhPham.objects.filter(thanh_pham=tp, ngay_san_xuat__lt=from_date_str)
        qs_tieu_thu_truoc = TieuThuThanhPham.objects.filter(thanh_pham=tp, ngay_tieu_thu__lt=from_date_str)
        ton_dau = sum([sx.so_luong for sx in qs_san_xuat_truoc]) - sum([tt.so_luong for tt in qs_tieu_thu_truoc])

        # Tính sản xuất và tiêu thụ trong kỳ
        qs_san_xuat_ky = SanXuatThanhPham.objects.filter(thanh_pham=tp, ngay_san_xuat__gte=from_date_str, ngay_san_xuat__lte=to_date_str)
        qs_tieu_thu_ky = TieuThuThanhPham.objects.filter(thanh_pham=tp, ngay_tieu_thu__gte=from_date_str, ngay_tieu_thu__lte=to_date_str)
        tong_san_xuat_ky = sum([sx.so_luong for sx in qs_san_xuat_ky])
        tong_tieu_thu_ky = sum([tt.so_luong for tt in qs_tieu_thu_ky])

        # Tồn cuối XN1 = Tồn đầu + Sản xuất trong kỳ - Tiêu thụ trong kỳ
        ton_cuoi_xn1 = ton_dau + tong_san_xuat_ky - tong_tieu_thu_ky

        ton_kho_detail_list.append({
            'ten_thanh_pham': tp.ten_thanh_pham,
            'don_vi_tinh': tp.don_vi_tinh,
            'ton_dau': ton_dau,
            'tong_san_xuat': tong_san_xuat_ky,
            'tong_tieu_thu': tong_tieu_thu_ky,
            'ton_cuoi_xn1': ton_cuoi_xn1,
            'ghi_chu': '',
        })

    # 1. Chi tiết sản xuất theo thành phẩm (for cards)
    san_xuat_detail = {}
    for sx in san_xuat_period:
        tp_name = sx.thanh_pham.ten_thanh_pham
        if tp_name not in san_xuat_detail:
            san_xuat_detail[tp_name] = {
                'ten_thanh_pham': tp_name,
                'don_vi_tinh': sx.thanh_pham.don_vi_tinh,
                'so_luong': 0
            }
        san_xuat_detail[tp_name]['so_luong'] += float(sx.so_luong)

    # 2. Chi tiết tiêu thụ theo thành phẩm (for cards)
    tieu_thu_detail = {}
    for tt in tieu_thu_period:
        tp_name = tt.thanh_pham.ten_thanh_pham
        if tp_name not in tieu_thu_detail:
            tieu_thu_detail[tp_name] = {
                'ten_thanh_pham': tp_name,
                'don_vi_tinh': tt.thanh_pham.don_vi_tinh,
                'so_luong': 0
            }
        tieu_thu_detail[tp_name]['so_luong'] += float(tt.so_luong)

    # 3. Chi tiết khách hàng và thành phẩm
    khach_hang_detail = {}
    for tt in tieu_thu_period:
        if tt.ho_khach_hang:
            kh_name = tt.ho_khach_hang
            tp_name = tt.thanh_pham.ten_thanh_pham

            if kh_name not in khach_hang_detail:
                khach_hang_detail[kh_name] = {
                    'ho_khach_hang': kh_name,
                    'thanh_pham': {},
                    'tong_so_luong': 0
                }

            if tp_name not in khach_hang_detail[kh_name]['thanh_pham']:
                khach_hang_detail[kh_name]['thanh_pham'][tp_name] = {
                    'ten_thanh_pham': tp_name,
                    'don_vi_tinh': tt.thanh_pham.don_vi_tinh,
                    'so_luong': 0
                }

            khach_hang_detail[kh_name]['thanh_pham'][tp_name]['so_luong'] += float(tt.so_luong)
            khach_hang_detail[kh_name]['tong_so_luong'] += float(tt.so_luong)

    # 4. Tồn kho chi tiết và cảnh báo hết hàng
    ton_kho_detail = []
    canh_bao_detail = []

    for tp in danh_muc:
        qs_san_xuat = SanXuatThanhPham.objects.filter(thanh_pham=tp, ngay_san_xuat__lte=to_date_str)
        qs_tieu_thu = TieuThuThanhPham.objects.filter(thanh_pham=tp, ngay_tieu_thu__lte=to_date_str)
        tong_san_xuat = sum([sx.so_luong for sx in qs_san_xuat])
        tong_tieu_thu = sum([tt.so_luong for tt in qs_tieu_thu])
        ton_cuoi = tong_san_xuat - tong_tieu_thu

        if ton_cuoi > 0:
            ton_kho_detail.append({
                'ten_thanh_pham': tp.ten_thanh_pham,
                'don_vi_tinh': tp.don_vi_tinh,
                'ton_cuoi': ton_cuoi
            })

        # Cảnh báo hết hàng (tồn kho < 1000)
        if ton_cuoi < 1000:
            canh_bao_detail.append({
                'ten_thanh_pham': tp.ten_thanh_pham,
                'don_vi_tinh': tp.don_vi_tinh,
                'ton_cuoi': ton_cuoi
            })

    # Debug prints
    print(f"DEBUG: san_xuat_detail count: {len(san_xuat_detail)}")
    print(f"DEBUG: tieu_thu_detail count: {len(tieu_thu_detail)}")
    print(f"DEBUG: khach_hang_detail count: {len(khach_hang_detail)}")
    print(f"DEBUG: ton_kho_detail count: {len(ton_kho_detail)}")
    print(f"DEBUG: canh_bao_detail count: {len(canh_bao_detail)}")

    context = {
        'today': today,
        'from_date': from_date,  # date object for template display
        'to_date': to_date,      # date object for template display
        'from_date_str': from_date_str,  # string for form input
        'to_date_str': to_date_str,      # string for form input
        'total_san_xuat': total_san_xuat,
        'total_tieu_thu': total_tieu_thu,
        'total_ton_kho': total_ton_kho,
        'total_khach_hang': total_khach_hang,
        'canh_bao_het_hang': canh_bao_het_hang,
        # Dữ liệu chi tiết cho modals (aggregated for cards)
        'san_xuat_detail': list(san_xuat_detail.values()),
        'tieu_thu_detail': list(tieu_thu_detail.values()),
        'khach_hang_detail': list(khach_hang_detail.values()),
        'ton_kho_detail': ton_kho_detail,
        'canh_bao_detail': canh_bao_detail,
        # Dữ liệu chi tiết cho modal tables (full records)
        'san_xuat_detail_list': san_xuat_detail_list,
        'tieu_thu_detail_list': tieu_thu_detail_list,
        'ton_kho_detail_list': ton_kho_detail_list,
        'khach_hang_detail_list': tieu_thu_detail_list.filter(ho_khach_hang__isnull=False).exclude(ho_khach_hang=''),
        # Dữ liệu cho biểu đồ
        'san_xuat_period': san_xuat_period,
        'tieu_thu_period': tieu_thu_period,
    }

    # Tính toán dữ liệu cho biểu đồ
    import json
    from collections import defaultdict

    # 1. Biểu đồ Tiêu thụ thành phẩm (Bar chart)
    tieu_thu_chart_data = defaultdict(float)
    for item in tieu_thu_detail_list:
        tieu_thu_chart_data[item.thanh_pham.ten_thanh_pham] += float(item.so_luong)

    chart_tieu_thu_labels = list(tieu_thu_chart_data.keys())
    chart_tieu_thu_data = list(tieu_thu_chart_data.values())

    # 2. Biểu đồ Hộ khách hàng tiêu thụ (Pie chart)
    khach_hang_chart_data = defaultdict(float)
    for item in tieu_thu_detail_list:
        if item.ho_khach_hang and item.ho_khach_hang.strip():
            khach_hang_chart_data[item.ho_khach_hang] += float(item.so_luong)

    chart_khach_hang_labels = list(khach_hang_chart_data.keys())
    chart_khach_hang_data = list(khach_hang_chart_data.values())

    # Fallback data nếu không có dữ liệu
    if not chart_tieu_thu_labels:
        chart_tieu_thu_labels = ['Chưa có dữ liệu']
        chart_tieu_thu_data = [0]

    if not chart_khach_hang_labels:
        chart_khach_hang_labels = ['Chưa có dữ liệu']
        chart_khach_hang_data = [0]

    # Debug: In ra dữ liệu biểu đồ
    print(f"DEBUG Chart - Tiêu thụ labels: {chart_tieu_thu_labels}")
    print(f"DEBUG Chart - Tiêu thụ data: {chart_tieu_thu_data}")
    print(f"DEBUG Chart - Khách hàng labels: {chart_khach_hang_labels}")
    print(f"DEBUG Chart - Khách hàng data: {chart_khach_hang_data}")
    print(f"DEBUG Chart - Tổng số record tiêu thụ: {tieu_thu_detail_list.count()}")

    # Thêm dữ liệu biểu đồ vào context
    context.update({
        'chart_tieu_thu_labels': json.dumps(chart_tieu_thu_labels),
        'chart_tieu_thu_data': json.dumps(chart_tieu_thu_data),
        'chart_khach_hang_labels': json.dumps(chart_khach_hang_labels),
        'chart_khach_hang_data': json.dumps(chart_khach_hang_data),
    })

    return render(request, 'z115_app/thanh_pham_simple.html', context)


@login_required
def danh_muc_thanh_pham(request):
    danh_muc = DanhMucThanhPham.objects.all()
    is_quan_ly = request.user.username in ["QUANGBNV", "hungpc892"]
    return render(request, 'z115_app/danh_muc_thanh_pham.html', {
        'danh_muc': danh_muc,
        'is_quan_ly': is_quan_ly,
    })

@login_required
def them_thanh_pham(request):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền thêm thành phẩm.")
    if request.method == "POST":
        ten_thanh_pham = request.POST.get("ten_thanh_pham", "").strip()
        don_vi_tinh = request.POST.get("don_vi_tinh", "").strip()
        ho_khach_hang = request.POST.get("ho_khach_hang", "").strip()
        noi_tieu_thu = request.POST.get("noi_tieu_thu", "").strip()
        phan_xuong_san_xuat = request.POST.get("phan_xuong_san_xuat", "").strip()

        # Kiểm tra có ít nhất 1 trường có dữ liệu
        fields = [ten_thanh_pham, don_vi_tinh, ho_khach_hang, noi_tieu_thu, phan_xuong_san_xuat]
        if not any(field for field in fields):
            messages.error(request, "Vui lòng nhập ít nhất một thông tin thành phẩm.")
            return redirect('danh_muc_thanh_pham')

        # Tạo thành phẩm mới
        DanhMucThanhPham.objects.create(
            ten_thanh_pham=ten_thanh_pham or "",
            don_vi_tinh=don_vi_tinh or "",
            ho_khach_hang=ho_khach_hang or "",
            noi_tieu_thu=noi_tieu_thu or "",
            phan_xuong_san_xuat=phan_xuong_san_xuat or ""
        )
        messages.success(request, "Đã thêm thành phẩm mới.")
    return redirect('danh_muc_thanh_pham')

@login_required
def sua_thanh_pham(request, thanh_pham_id):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền sửa thành phẩm.")
    tp = get_object_or_404(DanhMucThanhPham, id=thanh_pham_id)
    if request.method == "POST":
        ten_thanh_pham = request.POST.get("ten_thanh_pham", "").strip()
        don_vi_tinh = request.POST.get("don_vi_tinh", "").strip()
        ho_khach_hang = request.POST.get("ho_khach_hang", "").strip()
        noi_tieu_thu = request.POST.get("noi_tieu_thu", "").strip()
        phan_xuong_san_xuat = request.POST.get("phan_xuong_san_xuat", "").strip()

        # Kiểm tra có ít nhất 1 trường có dữ liệu
        fields = [ten_thanh_pham, don_vi_tinh, ho_khach_hang, noi_tieu_thu, phan_xuong_san_xuat]
        if not any(field for field in fields):
            messages.error(request, "Vui lòng nhập ít nhất một thông tin thành phẩm.")
            return render(request, 'z115_app/sua_thanh_pham.html', {'thanh_pham': tp})

        # Cập nhật thành phẩm
        tp.ten_thanh_pham = ten_thanh_pham or ""
        tp.don_vi_tinh = don_vi_tinh or ""
        tp.ho_khach_hang = ho_khach_hang or ""
        tp.noi_tieu_thu = noi_tieu_thu or ""
        tp.phan_xuong_san_xuat = phan_xuong_san_xuat or ""
        tp.save()
        messages.success(request, "Đã cập nhật thành phẩm.")
        return redirect('danh_muc_thanh_pham')
    return render(request, 'z115_app/sua_thanh_pham.html', {'thanh_pham': tp})

@login_required
def xoa_thanh_pham(request, thanh_pham_id):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền xoá thành phẩm.")
    tp = get_object_or_404(DanhMucThanhPham, id=thanh_pham_id)
    tp.delete()
    messages.success(request, "Đã xoá thành phẩm.")
    return redirect('danh_muc_thanh_pham')
# --- SẢN XUẤT THÀNH PHẨM ---
@login_required
def san_xuat_thanh_pham(request):
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    today = date.today()

    # Lọc dữ liệu sản xuất
    if from_date and to_date:
        # Nếu có filter thì lọc theo khoảng thời gian
        san_xuat_list = SanXuatThanhPham.objects.filter(
            ngay_san_xuat__gte=from_date,
            ngay_san_xuat__lte=to_date
        ).select_related('thanh_pham').order_by('-ngay_san_xuat')
    else:
        # Nếu không có filter thì hiển thị tất cả (hoặc 30 ngày gần nhất)
        san_xuat_list = SanXuatThanhPham.objects.all().select_related('thanh_pham').order_by('-ngay_san_xuat')[:100]
        # Set default values cho form
        if not from_date:
            from_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        if not to_date:
            to_date = today.strftime('%Y-%m-%d')

    danh_muc = DanhMucThanhPham.objects.all()
    is_quan_ly = request.user.username in ["QUANGBNV", "hungpc892"]

    return render(request, 'z115_app/san_xuat_thanh_pham.html', {
        'san_xuat_list': san_xuat_list,
        'danh_muc': danh_muc,
        'is_quan_ly': is_quan_ly,
        'from_date': from_date,
        'to_date': to_date,
    })

@login_required
def them_san_xuat_thanh_pham(request):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền thêm sản xuất.")
    if request.method == "POST":
        ngay_san_xuat = request.POST.get("ngay_san_xuat")
        thanh_pham_id = request.POST.get("thanh_pham")  # Hidden field từ autocomplete
        ten_thanh_pham = request.POST.get("ten_thanh_pham")  # Text input
        so_luong = request.POST.get("so_luong")
        phan_xuong_san_xuat = request.POST.get("phan_xuong_san_xuat")
        ghi_chu = request.POST.get("ghi_chu")

        if ngay_san_xuat and so_luong:
            # Tìm thành phẩm theo ID (nếu chọn từ autocomplete) hoặc tên
            thanh_pham = None
            if thanh_pham_id:
                try:
                    thanh_pham = DanhMucThanhPham.objects.get(id=thanh_pham_id)
                except DanhMucThanhPham.DoesNotExist:
                    pass

            # Nếu không tìm thấy theo ID, tìm theo tên
            if not thanh_pham and ten_thanh_pham:
                thanh_pham, created = DanhMucThanhPham.objects.get_or_create(
                    ten_thanh_pham=ten_thanh_pham,
                    defaults={'don_vi_tinh': '', 'phan_xuong_san_xuat': phan_xuong_san_xuat or ''}
                )

            if thanh_pham:
                # Auto-fill thông tin từ danh mục nếu không nhập
                if not phan_xuong_san_xuat:
                    phan_xuong_san_xuat = thanh_pham.phan_xuong_san_xuat

                SanXuatThanhPham.objects.create(
                    ngay_san_xuat=ngay_san_xuat,
                    thanh_pham=thanh_pham,
                    so_luong=so_luong,
                    phan_xuong_san_xuat=phan_xuong_san_xuat or "",
                    ghi_chu=ghi_chu or ""
                )
                messages.success(request, "Đã thêm sản xuất thành phẩm.")
            else:
                messages.error(request, "Không tìm thấy thành phẩm.")
        else:
            messages.error(request, "Vui lòng điền đầy đủ thông tin bắt buộc.")
    return redirect('san_xuat_thanh_pham')

@login_required
def sua_san_xuat_thanh_pham(request, san_xuat_id):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền sửa sản xuất.")

    sx = get_object_or_404(SanXuatThanhPham, id=san_xuat_id)

    if request.method == "POST":
        ngay_san_xuat = request.POST.get("ngay_san_xuat")
        thanh_pham_id = request.POST.get("thanh_pham")
        ten_thanh_pham = request.POST.get("ten_thanh_pham")
        so_luong = request.POST.get("so_luong")
        phan_xuong_san_xuat = request.POST.get("phan_xuong_san_xuat")
        ghi_chu = request.POST.get("ghi_chu")

        if ngay_san_xuat and so_luong:
            # Tìm thành phẩm theo ID hoặc tên
            thanh_pham = None
            if thanh_pham_id:
                try:
                    thanh_pham = DanhMucThanhPham.objects.get(id=thanh_pham_id)
                except DanhMucThanhPham.DoesNotExist:
                    pass

            if not thanh_pham and ten_thanh_pham:
                thanh_pham, created = DanhMucThanhPham.objects.get_or_create(
                    ten_thanh_pham=ten_thanh_pham,
                    defaults={'don_vi_tinh': '', 'phan_xuong_san_xuat': phan_xuong_san_xuat or ''}
                )

            if thanh_pham:
                sx.ngay_san_xuat = ngay_san_xuat
                sx.thanh_pham = thanh_pham
                sx.so_luong = so_luong
                sx.phan_xuong_san_xuat = phan_xuong_san_xuat or ""
                sx.ghi_chu = ghi_chu or ""
                sx.save()
                messages.success(request, "Đã cập nhật sản xuất thành phẩm.")
            else:
                messages.error(request, "Không tìm thấy thành phẩm.")
        else:
            messages.error(request, "Vui lòng điền đầy đủ thông tin bắt buộc.")
        return redirect('san_xuat_thanh_pham')

    # GET request - hiển thị form sửa
    danh_muc = DanhMucThanhPham.objects.all()
    return render(request, 'z115_app/sua_san_xuat_thanh_pham.html', {
        'san_xuat': sx,
        'danh_muc': danh_muc,
    })

@login_required
def xoa_san_xuat_thanh_pham(request, san_xuat_id):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền xoá sản xuất.")
    sx = get_object_or_404(SanXuatThanhPham, id=san_xuat_id)
    sx.delete()
    messages.success(request, "Đã xoá sản xuất thành phẩm.")
    return redirect('san_xuat_thanh_pham')

# --- TIÊU THỤ THÀNH PHẨM ---
@login_required
def tieu_thu(request):
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    today = date.today()

    # Lọc dữ liệu tiêu thụ
    if from_date and to_date:
        # Nếu có filter thì lọc theo khoảng thời gian
        tieu_thu_list = TieuThuThanhPham.objects.filter(
            ngay_tieu_thu__gte=from_date,
            ngay_tieu_thu__lte=to_date
        ).select_related('thanh_pham').order_by('-ngay_tieu_thu')
    else:
        # Nếu không có filter thì hiển thị tất cả (hoặc 30 ngày gần nhất)
        tieu_thu_list = TieuThuThanhPham.objects.all().select_related('thanh_pham').order_by('-ngay_tieu_thu')[:100]
        # Set default values cho form
        if not from_date:
            from_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')
        if not to_date:
            to_date = today.strftime('%Y-%m-%d')

    danh_muc = DanhMucThanhPham.objects.all()
    is_quan_ly = request.user.username in ["QUANGBNV", "hungpc892"]

    return render(request, 'z115_app/tieu_thu.html', {
        'tieu_thu_list': tieu_thu_list,
        'danh_muc': danh_muc,
        'is_quan_ly': is_quan_ly,
        'from_date': from_date,
        'to_date': to_date,
    })

@login_required
def them_tieu_thu(request):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền thêm tiêu thụ.")
    if request.method == "POST":
        ngay_tieu_thu = request.POST.get("ngay_tieu_thu")
        thanh_pham_id = request.POST.get("thanh_pham")  # Hidden field từ autocomplete
        ten_thanh_pham = request.POST.get("ten_thanh_pham")  # Text input
        so_luong = request.POST.get("so_luong")
        ho_khach_hang = request.POST.get("ho_khach_hang")
        noi_tieu_thu = request.POST.get("noi_tieu_thu")
        ghi_chu = request.POST.get("ghi_chu")

        if ngay_tieu_thu and so_luong:
            # Tìm thành phẩm theo ID (nếu chọn từ autocomplete) hoặc tên
            thanh_pham = None
            if thanh_pham_id:
                try:
                    thanh_pham = DanhMucThanhPham.objects.get(id=thanh_pham_id)
                except DanhMucThanhPham.DoesNotExist:
                    pass

            # Nếu không tìm thấy theo ID, tìm theo tên
            if not thanh_pham and ten_thanh_pham:
                thanh_pham, created = DanhMucThanhPham.objects.get_or_create(
                    ten_thanh_pham=ten_thanh_pham,
                    defaults={
                        'don_vi_tinh': '',
                        'ho_khach_hang': ho_khach_hang or '',
                        'noi_tieu_thu': noi_tieu_thu or ''
                    }
                )

            if thanh_pham:
                # Auto-fill thông tin từ danh mục nếu không nhập
                if not ho_khach_hang:
                    ho_khach_hang = thanh_pham.ho_khach_hang
                if not noi_tieu_thu:
                    noi_tieu_thu = thanh_pham.noi_tieu_thu

                TieuThuThanhPham.objects.create(
                    ngay_tieu_thu=ngay_tieu_thu,
                    thanh_pham=thanh_pham,
                    so_luong=so_luong,
                    ho_khach_hang=ho_khach_hang or "",
                    noi_tieu_thu=noi_tieu_thu or "",
                    ghi_chu=ghi_chu or ""
                )
                messages.success(request, "Đã thêm tiêu thụ thành phẩm.")
            else:
                messages.error(request, "Không tìm thấy thành phẩm.")
        else:
            messages.error(request, "Vui lòng điền đầy đủ thông tin bắt buộc.")
    return redirect('tieu_thu')

@login_required
def sua_tieu_thu(request, tieu_thu_id):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền sửa tiêu thụ.")

    tt = get_object_or_404(TieuThuThanhPham, id=tieu_thu_id)

    if request.method == "POST":
        ngay_tieu_thu = request.POST.get("ngay_tieu_thu")
        thanh_pham_id = request.POST.get("thanh_pham")
        ten_thanh_pham = request.POST.get("ten_thanh_pham")
        so_luong = request.POST.get("so_luong")
        ho_khach_hang = request.POST.get("ho_khach_hang")
        noi_tieu_thu = request.POST.get("noi_tieu_thu")
        ghi_chu = request.POST.get("ghi_chu")

        if ngay_tieu_thu and so_luong:
            # Tìm thành phẩm theo ID hoặc tên
            thanh_pham = None
            if thanh_pham_id:
                try:
                    thanh_pham = DanhMucThanhPham.objects.get(id=thanh_pham_id)
                except DanhMucThanhPham.DoesNotExist:
                    pass

            if not thanh_pham and ten_thanh_pham:
                thanh_pham, created = DanhMucThanhPham.objects.get_or_create(
                    ten_thanh_pham=ten_thanh_pham,
                    defaults={
                        'don_vi_tinh': '',
                        'ho_khach_hang': ho_khach_hang or '',
                        'noi_tieu_thu': noi_tieu_thu or ''
                    }
                )

            if thanh_pham:
                tt.ngay_tieu_thu = ngay_tieu_thu
                tt.thanh_pham = thanh_pham
                tt.so_luong = so_luong
                tt.ho_khach_hang = ho_khach_hang or ""
                tt.noi_tieu_thu = noi_tieu_thu or ""
                tt.ghi_chu = ghi_chu or ""
                tt.save()
                messages.success(request, "Đã cập nhật tiêu thụ thành phẩm.")
            else:
                messages.error(request, "Không tìm thấy thành phẩm.")
        else:
            messages.error(request, "Vui lòng điền đầy đủ thông tin bắt buộc.")
        return redirect('tieu_thu')

    # GET request - hiển thị form sửa
    danh_muc = DanhMucThanhPham.objects.all()
    return render(request, 'z115_app/sua_tieu_thu.html', {
        'tieu_thu': tt,
        'danh_muc': danh_muc,
    })

@login_required
def xoa_tieu_thu(request, tieu_thu_id):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền xoá tiêu thụ.")
    tt = get_object_or_404(TieuThuThanhPham, id=tieu_thu_id)
    tt.delete()
    messages.success(request, "Đã xoá tiêu thụ thành phẩm.")
    return redirect('tieu_thu')

@login_required
def api_khach_hang_autocomplete(request):
    """API để lấy danh sách hộ khách hàng cho autocomplete"""
    query = request.GET.get('q', '').strip()

    if len(query) < 1:
        return JsonResponse({'results': []})

    # Lấy tất cả hộ khách hàng từ danh mục thành phẩm
    khach_hang_list = DanhMucThanhPham.objects.filter(
        ho_khach_hang__icontains=query
    ).exclude(ho_khach_hang='').values_list('ho_khach_hang', flat=True).distinct()[:10]

    results = [{'text': kh, 'value': kh} for kh in khach_hang_list]

    return JsonResponse({'results': results})

@login_required
def api_noi_tieu_thu_autocomplete(request):
    """API để lấy danh sách nơi tiêu thụ cho autocomplete"""
    query = request.GET.get('q', '').strip()

    if len(query) < 1:
        return JsonResponse({'results': []})

    # Lấy tất cả nơi tiêu thụ từ danh mục thành phẩm
    noi_tieu_thu_list = DanhMucThanhPham.objects.filter(
        noi_tieu_thu__icontains=query
    ).exclude(noi_tieu_thu='').values_list('noi_tieu_thu', flat=True).distinct()[:10]

    results = [{'text': ntt, 'value': ntt} for ntt in noi_tieu_thu_list]

    return JsonResponse({'results': results})
# --- TỒN KHO THÀNH PHẨM ---
@login_required
def ton_kho_thanh_pham(request):
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    today = date.today()

    # Mặc định hiển thị tháng hiện tại
    if not from_date:
        from_date = today.replace(day=1).strftime('%Y-%m-%d')
    if not to_date:
        to_date = today.strftime('%Y-%m-%d')

    # Tính tồn kho cho tất cả thành phẩm
    danh_muc = DanhMucThanhPham.objects.all()
    ton_kho_list = []
    is_quan_ly = request.user.username in ["QUANGBNV", "hungpc892"]

    for tp in danh_muc:
        # Tính tồn đầu (sản xuất - tiêu thụ trước from_date)
        san_xuat_truoc = SanXuatThanhPham.objects.filter(thanh_pham=tp, ngay_san_xuat__lt=from_date)
        tieu_thu_truoc = TieuThuThanhPham.objects.filter(thanh_pham=tp, ngay_tieu_thu__lt=from_date)
        ton_dau = sum([sx.so_luong for sx in san_xuat_truoc]) - sum([tt.so_luong for tt in tieu_thu_truoc])

        # Tổng sản xuất, tiêu thụ trong kỳ (from_date đến to_date)
        qs_san_xuat_ky = SanXuatThanhPham.objects.filter(
            thanh_pham=tp,
            ngay_san_xuat__gte=from_date,
            ngay_san_xuat__lte=to_date
        )
        qs_tieu_thu_ky = TieuThuThanhPham.objects.filter(
            thanh_pham=tp,
            ngay_tieu_thu__gte=from_date,
            ngay_tieu_thu__lte=to_date
        )
        tong_san_xuat_ky = sum([sx.so_luong for sx in qs_san_xuat_ky])
        tong_tieu_thu_ky = sum([tt.so_luong for tt in qs_tieu_thu_ky])

        # Tồn cuối XN1 = Tồn đầu + Sản xuất trong kỳ - Tiêu thụ trong kỳ
        ton_cuoi_xn1 = ton_dau + tong_san_xuat_ky - tong_tieu_thu_ky

        ton_kho_list.append({
            'id': tp.id,
            'ten_thanh_pham': tp.ten_thanh_pham,
            'don_vi_tinh': tp.don_vi_tinh,
            'ton_dau': ton_dau,
            'tong_san_xuat': tong_san_xuat_ky,
            'tong_tieu_thu': tong_tieu_thu_ky,
            'ton_cuoi_xn1': ton_cuoi_xn1,
            'ghi_chu': '',  # Có thể thêm logic ghi chú nếu cần
        })

    return render(request, 'z115_app/ton_kho_thanh_pham.html', {
        'ton_kho_list': ton_kho_list,
        'is_quan_ly': is_quan_ly,
        'from_date': from_date,
        'to_date': to_date,
    })

@login_required
def sua_ton_kho_thanh_pham(request, ton_kho_id):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền sửa tồn kho.")
    # Logic sửa tồn kho (nếu cần)
    return redirect('ton_kho_thanh_pham')

@login_required
def xoa_ton_kho_thanh_pham(request, ton_kho_id):
    if request.user.username not in ["QUANGBNV", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền xoá tồn kho.")
    # Logic xóa tồn kho (nếu cần)
    return redirect('ton_kho_thanh_pham')



@login_required
def tieu_thu_tncn(request):
    danh_muc = DanhMucThanhPham.objects.all()
    tieu_thu_list = TieuThuThanhPham.objects.select_related('thanh_pham').all()
    return render(request, 'z115_app/tieu_thu_tncn.html', {
        'danh_muc': danh_muc,
        'tieu_thu_list': tieu_thu_list,
    })
@login_required
def ton_kho_tp(request):
    danh_muc = DanhMucThanhPham.objects.all()
    # ton_kho_tp_list = ... # Tính toán tồn kho thành phẩm nếu cần
    return render(request, 'z115_app/ton_kho_tp.html', {
        'danh_muc': danh_muc,
        # 'ton_kho_tp_list': ton_kho_tp_list,
    })

@login_required
def san_xuat_tncn(request):
    danh_muc = DanhMucThanhPham.objects.all()
    san_xuat_list = SanXuatThanhPham.objects.select_related('thanh_pham').all()
    return render(request, 'z115_app/san_xuat_tncn.html', {
        'danh_muc': danh_muc,
        'san_xuat_list': san_xuat_list,
    })
@login_required
def them_vat_tu(request):
    if request.user.username != "hungpc892":
        return HttpResponseForbidden("Bạn không có quyền thêm vật tư.")
    if request.method == "POST":
        ten_vat_tu = request.POST.get("ten_vat_tu", "").strip()
        don_vi_tinh = request.POST.get("don_vi_tinh", "").strip()
        nha_cung_cap = request.POST.get("nha_cung_cap", "").strip()
        xuat_xu = request.POST.get("xuat_xu", "").strip()
        quy_cach = request.POST.get("quy_cach", "").strip()

        # Debug: In ra giá trị các trường
        print(f"DEBUG - ten_vat_tu: '{ten_vat_tu}'")
        print(f"DEBUG - don_vi_tinh: '{don_vi_tinh}'")
        print(f"DEBUG - nha_cung_cap: '{nha_cung_cap}'")
        print(f"DEBUG - xuat_xu: '{xuat_xu}'")
        print(f"DEBUG - quy_cach: '{quy_cach}'")

        # Kiểm tra có ít nhất 1 trường có dữ liệu
        fields = [ten_vat_tu, don_vi_tinh, nha_cung_cap, xuat_xu, quy_cach]
        print(f"DEBUG - fields: {fields}")
        print(f"DEBUG - any(field for field in fields): {any(field for field in fields)}")

        if not any(field for field in fields):
            messages.error(request, "Vui lòng nhập ít nhất một thông tin vật tư.")
            return redirect('danh_sach_vat_tu')

        # Tạo vật tư mới với các trường có thể rỗng
        DanhMucVatTu.objects.create(
            ten_vat_tu=ten_vat_tu or "",
            don_vi_tinh=don_vi_tinh or "",
            nha_cung_cap=nha_cung_cap or "",
            xuat_xu=xuat_xu or "",
            quy_cach=quy_cach or ""
        )
        messages.success(request, "Đã thêm vật tư mới.")
    return redirect('danh_sach_vat_tu')

@login_required
def sua_vat_tu(request, vat_tu_id):
    if request.user.username != "hungpc892":
        return HttpResponseForbidden("Bạn không có quyền sửa vật tư.")
    vt = get_object_or_404(DanhMucVatTu, id=vat_tu_id)
    if request.method == "POST":
        ten_vat_tu = request.POST.get("ten_vat_tu", "").strip()
        don_vi_tinh = request.POST.get("don_vi_tinh", "").strip()
        nha_cung_cap = request.POST.get("nha_cung_cap", "").strip()
        xuat_xu = request.POST.get("xuat_xu", "").strip()
        quy_cach = request.POST.get("quy_cach", "").strip()

        # Kiểm tra có ít nhất 1 trường có dữ liệu
        fields = [ten_vat_tu, don_vi_tinh, nha_cung_cap, xuat_xu, quy_cach]
        if not any(field for field in fields):
            messages.error(request, "Vui lòng nhập ít nhất một thông tin vật tư.")
            return render(request, 'z115_app/sua_vat_tu.html', {'vat_tu': vt})

        # Cập nhật vật tư với các trường có thể rỗng
        vt.ten_vat_tu = ten_vat_tu or ""
        vt.don_vi_tinh = don_vi_tinh or ""
        vt.nha_cung_cap = nha_cung_cap or ""
        vt.xuat_xu = xuat_xu or ""
        vt.quy_cach = quy_cach or ""
        vt.save()
        messages.success(request, "Đã cập nhật vật tư.")
        return redirect('danh_sach_vat_tu')
    return render(request, 'z115_app/sua_vat_tu.html', {'vat_tu': vt})

@login_required
def xoa_vat_tu(request, vat_tu_id):
    if request.user.username != "hungpc892":
        return HttpResponseForbidden("Bạn không có quyền xoá vật tư.")
    vt = get_object_or_404(DanhMucVatTu, id=vat_tu_id)
    vt.delete()
    messages.success(request, "Đã xoá vật tư.")
    return redirect('danh_sach_vat_tu')
@login_required
def ton_kho_kz24(request):
    if request.user.username not in ["PHANXUONGA6", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền truy cập.")
    danh_muc = DanhMucVatTu.objects.all()
    ton_kho_list = TonKhoKZ24.objects.select_related('vat_tu').order_by('-ngay')
    if request.method == "POST":
        ngay = request.POST.get("ngay")
        ten_vat_tu = request.POST.get("ten_vat_tu")
        so_luong = request.POST.get("so_luong")
        ghi_chu = request.POST.get("ghi_chu")
        vat_tu = DanhMucVatTu.objects.filter(ten_vat_tu=ten_vat_tu).first()
        if vat_tu and ngay and so_luong:
            TonKhoKZ24.objects.create(
                ngay=ngay,
                vat_tu=vat_tu,
                so_luong=so_luong,
                ghi_chu=ghi_chu
            )
            messages.success(request, "Đã cập nhật tồn kho KZ24 A6.")
            return redirect('ton_kho_kz24')
    return render(request, 'z115_app/ton_kho_kz24.html', {
        'danh_muc': danh_muc,
        'ton_kho_list': ton_kho_list,
    })

@login_required
def sua_ton_kho_kz24(request, ton_id):
    if request.user.username not in ["PHANXUONGA6", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền sửa tồn kho.")
    ton = get_object_or_404(TonKhoKZ24, id=ton_id)
    danh_muc = DanhMucVatTu.objects.all()
    if request.method == "POST":
        ngay = request.POST.get("ngay")
        ten_vat_tu = request.POST.get("ten_vat_tu")
        so_luong = request.POST.get("so_luong")
        ghi_chu = request.POST.get("ghi_chu")
        vat_tu = DanhMucVatTu.objects.filter(ten_vat_tu=ten_vat_tu).first()
        if vat_tu and ngay and so_luong:
            ton.ngay = ngay
            ton.vat_tu = vat_tu
            ton.so_luong = so_luong
            ton.ghi_chu = ghi_chu
            ton.save()
            messages.success(request, "Đã cập nhật tồn kho KZ24 A6.")
            return redirect('ton_kho_kz24')
    return render(request, 'z115_app/sua_ton_kho_kz24.html', {'ton': ton, 'danh_muc': danh_muc})

@login_required
def xoa_ton_kho_kz24(request, ton_id):
    if request.user.username not in ["PHANXUONGA6", "hungpc892"]:
        return HttpResponseForbidden("Bạn không có quyền xoá tồn kho.")
    ton = get_object_or_404(TonKhoKZ24, id=ton_id)
    if request.method == "POST":
        ton.delete()
        messages.success(request, "Đã xoá tồn kho KZ24 A6.")
        return redirect('ton_kho_kz24')
    return render(request, 'z115_app/xoa_ton_kho_kz24.html', {'ton': ton})