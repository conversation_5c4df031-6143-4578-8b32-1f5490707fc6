{% extends 'z115_app/base.html' %}
{% block title %}DANH MỤC VẬT TƯ{% endblock %}
{% block content %}
<div class="container mt-4">
    <h2 class="text-center mb-4">DANH MỤC VẬT TƯ</h2>
    <div class="table-responsive">
        <table class="table table-bordered table-striped align-middle">
            <thead class="table-success">
                <tr>
                    <th>STT</th>
                    <th>Tên vật tư</th>
                    <th>Đvt</th>
                    <th>Nhà cung cấp</th>
                    <th>Xuất xứ</th>
                    <th>Quy cách</th>
                    {% if is_super_hungpc892 %}
                    <th>Hành động</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for vt in danh_muc %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ vt.ten_vat_tu }}</td>
                    <td>{{ vt.don_vi_tinh }}</td>
                    <td>{{ vt.nha_cung_cap }}</td>
                    <td>{{ vt.xuat_xu }}</td>
                    <td>{{ vt.quy_cach }}</td>
                    {% if is_super_hungpc892 %}
                    <td>
                        <a href="{% url 'sua_vat_tu' vt.id %}" class="btn btn-sm btn-warning">Sửa</a>
                        <a href="{% url 'xoa_vat_tu' vt.id %}" class="btn btn-sm btn-danger"
                            onclick="return confirm('Bạn chắc chắn muốn xoá?');">Xoá</a>
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
                {% if is_super_hungpc892 %}
                <!-- Dòng thêm mới -->
                <tr id="add-row" style="display:none;">
                    <form method="post" action="{% url 'them_vat_tu' %}">
                        {% csrf_token %}
                        <td>#</td>
                        <td><input type="text" name="ten_vat_tu" class="form-control"></td>
                        <td><input type="text" name="don_vi_tinh" class="form-control"></td>
                        <td><input type="text" name="nha_cung_cap" class="form-control"></td>
                        <td><input type="text" name="xuat_xu" class="form-control"></td>
                        <td><input type="text" name="quy_cach" class="form-control"></td>
                        <td>
                            <button type="submit" class="btn btn-success btn-sm">Lưu</button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="hideAddRow()">Huỷ</button>
                        </td>
                    </form>
                </tr>
                {% endif %}
            </tbody>
        </table>
        {% if is_super_hungpc892 %}
        <button class="btn btn-primary" onclick="showAddRow()">Thêm mới</button>
        <script>
            function showAddRow() {
                document.getElementById('add-row').style.display = '';
            }
            function hideAddRow() {
                document.getElementById('add-row').style.display = 'none';
            }
        </script>
        {% endif %}

        <!-- Nút Quay lại -->
        <div class="mt-3">
            <a href="{% url 'vat_tu_pvsx' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> QUAY LẠI
            </a>
        </div>
    </div>
</div>

<script>
    function validateForm() {
        // Lấy tất cả input trong form thêm vật tư (tìm theo name attributes)
        const tenVatTu = document.querySelector('input[name="ten_vat_tu"]');
        const donViTinh = document.querySelector('input[name="don_vi_tinh"]');
        const nhaCungCap = document.querySelector('input[name="nha_cung_cap"]');
        const xuatXu = document.querySelector('input[name="xuat_xu"]');
        const quyCach = document.querySelector('input[name="quy_cach"]');

        // Kiểm tra có ít nhất 1 input có giá trị
        const values = [
            tenVatTu ? tenVatTu.value.trim() : '',
            donViTinh ? donViTinh.value.trim() : '',
            nhaCungCap ? nhaCungCap.value.trim() : '',
            xuatXu ? xuatXu.value.trim() : '',
            quyCach ? quyCach.value.trim() : ''
        ];

        const hasValue = values.some(value => value !== '');

        if (!hasValue) {
            alert('Vui lòng nhập ít nhất một thông tin vật tư.');
            return false;
        }

        return true;
    }
</script>
{% endblock %}