#!/usr/bin/env python
"""
Script sửa nhanh cho máy Admin
"""
import sqlite3
import os

def quick_fix():
    """Sửa nhanh bằng cách cập nhật migration table"""
    print("⚡ QUICK FIX CHO MÁY ADMIN")
    print("=" * 30)
    
    if not os.path.exists("db.sqlite3"):
        print("❌ Không tìm thấy db.sqlite3")
        return False
    
    conn = sqlite3.connect("db.sqlite3")
    cursor = conn.cursor()
    
    try:
        # Thêm tất cả migration còn thiếu
        migrations = [
            '0015_danhmucthanhpham_sanxuattncn_tieuthutncn',
            '0016_nhapvattu_quy_cach',
            '0017_tonkhokz24',
            '0018_danhmucthanhpham_phan_xuong_san_xuat_and_more'
        ]
        
        for migration in migrations:
            # Xóa nếu đã có
            cursor.execute("""
                DELETE FROM django_migrations 
                WHERE app = 'z115_app' AND name = ?;
            """, (migration,))
            
            # Thêm lại
            cursor.execute("""
                INSERT INTO django_migrations (app, name, applied) 
                VALUES ('z115_app', ?, datetime('now'));
            """, (migration,))
            
            print(f"✅ {migration}")
        
        conn.commit()
        conn.close()
        
        print("\n✅ HOÀN THÀNH!")
        print("🚀 Chạy: python manage.py runserver 192.170.5.186:8000")
        print("🌐 Test: http://192.170.5.186:8000/thanh-pham/")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        conn.rollback()
        conn.close()
        return False

if __name__ == "__main__":
    quick_fix()
